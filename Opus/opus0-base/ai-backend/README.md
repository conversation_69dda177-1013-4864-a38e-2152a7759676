# AI Backend

This is the AI backend built with Poetry and Python using FastAPI.

## SecureChat API

The SecureChat API provides encrypted chat functionality that protects message content at rest in MongoDB while maintaining the same streaming and LLM processing capabilities as regular chats.

### Overview

SecureChat uses symmetric encryption (Fernet) with a single backend key to encrypt all message content before storage. Messages remain plaintext during LLM processing and streaming, ensuring no impact on performance or functionality.

**Key Features:**
- ✅ Symmetric encryption for messages at rest
- ✅ Transparent decryption for frontend display
- ✅ No changes to LLM pipeline or streaming logic
- ✅ Minimal frontend integration (route names only)
- ✅ Text messages only (attachments untouched in MVP)

### Setup

#### 1. Generate Encryption Key

Generate a Fernet encryption key using Python:

```bash
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
```

#### 2. Configure Environment

Add the generated key to your `.env` file:

```bash
# SecureChat Encryption Configuration
CHAT_ENCRYPTION_KEY=your_generated_fernet_key_here
```

**⚠️ IMPORTANT:** Keep this key secure and backed up. Loss of the key means loss of all encrypted messages.

### API Endpoints

#### REST Endpoints
- `GET /secure_chats/{chat_id}` - Retrieve secure chat with decrypted messages
- `GET /secure_chats` - List all secure chat summaries
- `DELETE /secure_chats/{chat_id}` - Delete a secure chat

#### Socket Events
- `secure_chat_created` - Initialize a new secure chat
- `secure_chat_message` - Send encrypted user message
- `store_secure_ai_message` - Store encrypted AI response
- `secure_chat_chunk` - Receive streaming AI response chunks
- `secure_chat_error` - Handle encryption/processing errors

### Data Flow

1. **User Message**: Frontend → `secure_chat_message` → encrypt → MongoDB
2. **LLM Processing**: decrypt → process in memory → stream to frontend
3. **AI Response**: `store_secure_ai_message` → encrypt → MongoDB
4. **Retrieval**: MongoDB → decrypt → frontend display

### Database Schema

Secure chats are distinguished by the `is_secure: true` flag:

```javascript
{
  "conversation_id": "chat_12345",
  "user_id": ObjectId("..."),
  "is_secure": true,  // Identifies secure chats
  "messages": [
    {
      "role": "user",
      "content": "gAAAAABh...",  // Encrypted content
      "timestamp": ISODate("...")
    }
  ]
}
```

### Security Considerations

**What's Protected:**
- Message content encrypted at rest in MongoDB
- Automatic encryption/decryption at storage boundaries
- Secure key management via environment variables

**Limitations (MVP Scope):**
- Single symmetric key (no per-user keys)
- No forward secrecy
- No client-side encryption
- Attachments not encrypted (text messages only)

### Key Rotation

To rotate the encryption key:

1. Generate a new key: `python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"`
2. Update `CHAT_ENCRYPTION_KEY` in your environment
3. Restart the application

**Note:** Old messages encrypted with the previous key will become unreadable. Plan key rotation carefully.

### Frontend Integration

Minimal changes required - only route and event names differ:

```typescript
// Regular chat
socket.emit('chat_message', data);
fetch('/api/chats/123');

// Secure chat
socket.emit('secure_chat_message', data);
fetch('/api/secure_chats/123');
```

### Error Handling

The system gracefully handles encryption failures:
- Invalid keys: Raises `EncryptionError` on startup
- Corrupted messages: Skips and logs errors, continues processing
- Missing keys: Clear error messages in logs

### Testing

Verify encryption is working:

1. Send a secure chat message
2. Check MongoDB directly - content should be encrypted (base64 string starting with `gAAAAAB`)
3. Retrieve via API - content should be decrypted plaintext

### Development

The SecureChat implementation mirrors the regular chat system:
- `app/api/routes/secure_chat.py` - Core chat processing with encryption
- `app/api/routes/secure_records.py` - REST endpoints for retrieval
- `app/utils/encryption.py` - Fernet encryption utilities
- Socket handlers in `app/main.py` - Real-time communication