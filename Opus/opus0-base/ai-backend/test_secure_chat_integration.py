#!/usr/bin/env python3
"""
Comprehensive integration test for SecureChat API.

This test verifies:
1. MongoDB never stores plaintext content for secure chats
2. Encryption/decryption works correctly in real user scenarios
3. Complete end-to-end flow from message creation to retrieval
4. Error handling and edge cases
5. Separation between regular and secure chats
"""

import os
import sys
import asyncio
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

# Set test environment
os.environ["CHAT_ENCRYPTION_KEY"] = "ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg="

class SecureChatIntegrationTest:
    """Comprehensive integration test for SecureChat functionality."""
    
    def __init__(self):
        self.test_chat_id = f"test_secure_chat_{int(time.time())}"
        self.test_user_id = "507f1f77bcf86cd799439011"  # Valid ObjectId format
        self.test_messages = [
            "Hello, this is my first secure message!",
            "Can you help me with a sensitive question?",
            "Here's some confidential information: API_KEY=secret123",
            "Testing special characters: àáâãäåæçèéêë 中文 🔒🛡️",
            "",  # Empty message test
            "A" * 1000,  # Long message test
        ]
        self.db_collection = None
        
    def setup_database(self):
        """Initialize database connection for direct MongoDB verification."""
        try:
            from app.db import chat_histories_collection
            self.db_collection = chat_histories_collection
            print("✅ Database connection established")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
    
    def cleanup_test_data(self):
        """Clean up test data from database."""
        if self.db_collection:
            result = self.db_collection.delete_many({
                "conversation_id": {"$regex": "^test_secure_chat_"}
            })
            print(f"🧹 Cleaned up {result.deleted_count} test chat documents")
    
    async def test_secure_chat_creation(self) -> bool:
        """Test secure chat document creation."""
        print("\n📝 Testing secure chat creation...")
        
        try:
            from app.api.routes.secure_chat import process_secure_message
            
            # Create a secure chat by sending first message
            message_gen = process_secure_message(
                message=self.test_messages[0],
                chat_id=self.test_chat_id,
                user_id=self.test_user_id,
                model_choice="Gemini 2.5 Flash",
                message_id="test_msg_001"
            )
            
            # Consume the generator (simulate streaming)
            ai_response_chunks = []
            async for chunk in message_gen:
                ai_response_chunks.append(chunk)
            
            ai_response = "".join(ai_response_chunks)
            
            # Verify chat document was created in MongoDB
            chat_doc = self.db_collection.find_one({"conversation_id": self.test_chat_id})
            
            if not chat_doc:
                print("❌ Chat document not created in MongoDB")
                return False
            
            # Verify secure flag is set
            if not chat_doc.get("is_secure"):
                print("❌ Chat document missing is_secure flag")
                return False
            
            # Verify user message is encrypted in MongoDB
            messages = chat_doc.get("messages", [])
            if not messages:
                print("❌ No messages found in chat document")
                return False
            
            user_message = messages[0]
            encrypted_content = user_message.get("content", "")
            
            # Verify content is encrypted (Fernet tokens start with gAAAAA)
            if not encrypted_content.startswith("gAAAAA"):
                print(f"❌ Message content not encrypted: {encrypted_content}")
                return False
            
            # Verify encrypted content is NOT the original plaintext
            if encrypted_content == self.test_messages[0]:
                print("❌ Message content is plaintext, not encrypted!")
                return False
            
            print(f"✅ Secure chat created successfully")
            print(f"   - Chat ID: {self.test_chat_id}")
            print(f"   - is_secure: {chat_doc.get('is_secure')}")
            print(f"   - Encrypted content: {encrypted_content[:50]}...")
            print(f"   - AI response generated: {len(ai_response)} characters")
            
            return True
            
        except Exception as e:
            print(f"❌ Secure chat creation failed: {e}")
            return False
    
    async def test_multiple_message_encryption(self) -> bool:
        """Test multiple messages are all encrypted in MongoDB."""
        print("\n💬 Testing multiple message encryption...")
        
        try:
            from app.api.routes.secure_chat import process_secure_message
            
            # Send multiple messages
            for i, message in enumerate(self.test_messages[1:4], 2):  # Skip first message (already sent)
                message_gen = process_secure_message(
                    message=message,
                    chat_id=self.test_chat_id,
                    user_id=self.test_user_id,
                    message_id=f"test_msg_{i:03d}"
                )
                
                # Consume the generator
                async for chunk in message_gen:
                    pass  # Just consume, don't store
            
            # Verify all messages are encrypted in MongoDB
            chat_doc = self.db_collection.find_one({"conversation_id": self.test_chat_id})
            messages = chat_doc.get("messages", [])
            
            user_messages = [msg for msg in messages if msg.get("role") == "user"]
            
            if len(user_messages) < 4:  # Should have 4 user messages now
                print(f"❌ Expected 4 user messages, found {len(user_messages)}")
                return False
            
            # Verify each user message is encrypted
            for i, msg in enumerate(user_messages):
                content = msg.get("content", "")
                original = self.test_messages[i]
                
                # Check encryption format
                if not content.startswith("gAAAAA"):
                    print(f"❌ Message {i+1} not encrypted: {content}")
                    return False
                
                # Check it's not plaintext
                if content == original:
                    print(f"❌ Message {i+1} is plaintext: {content}")
                    return False
                
                print(f"✅ Message {i+1} properly encrypted: {content[:30]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ Multiple message encryption test failed: {e}")
            return False
    
    def test_secure_chat_retrieval(self) -> bool:
        """Test secure chat retrieval and decryption via REST API."""
        print("\n🔍 Testing secure chat retrieval and decryption...")
        
        try:
            from app.api.routes.secure_records import fetch_secure_chat
            
            # Retrieve the secure chat
            chat_data = fetch_secure_chat(self.test_chat_id, self.test_user_id)
            
            if not chat_data:
                print("❌ Failed to retrieve secure chat")
                return False
            
            # Verify chat metadata
            if chat_data.get("id") != self.test_chat_id:
                print(f"❌ Wrong chat ID: {chat_data.get('id')}")
                return False
            
            # Verify messages are decrypted
            messages = chat_data.get("messages", [])
            user_messages = [msg for msg in messages if msg.get("isUser")]
            
            if len(user_messages) < 4:
                print(f"❌ Expected 4 user messages, found {len(user_messages)}")
                return False
            
            # Verify each message is properly decrypted
            for i, msg in enumerate(user_messages):
                decrypted_content = msg.get("content", "")
                expected_content = self.test_messages[i]
                
                if decrypted_content != expected_content:
                    print(f"❌ Message {i+1} decryption failed:")
                    print(f"   Expected: {expected_content}")
                    print(f"   Got: {decrypted_content}")
                    return False
                
                print(f"✅ Message {i+1} correctly decrypted: {decrypted_content[:50]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ Secure chat retrieval test failed: {e}")
            return False
    
    def test_regular_vs_secure_separation(self) -> bool:
        """Test that regular and secure chats are properly separated."""
        print("\n🔄 Testing regular vs secure chat separation...")
        
        try:
            from app.api.routes.chat import process_message
            from app.api.routes.records import fetch_chat
            
            # Create a regular chat with same user
            regular_chat_id = f"test_regular_chat_{int(time.time())}"
            
            async def create_regular_chat():
                message_gen = process_message(
                    message="This is a regular (non-secure) message",
                    chat_id=regular_chat_id,
                    user_id=self.test_user_id,
                    message_id="regular_msg_001"
                )
                async for chunk in message_gen:
                    pass
            
            # Create regular chat
            asyncio.run(create_regular_chat())
            
            # Verify regular chat is NOT marked as secure
            regular_doc = self.db_collection.find_one({"conversation_id": regular_chat_id})
            if regular_doc.get("is_secure"):
                print("❌ Regular chat incorrectly marked as secure")
                return False
            
            # Verify regular chat content is NOT encrypted
            regular_messages = regular_doc.get("messages", [])
            if regular_messages:
                regular_content = regular_messages[0].get("content", "")
                if regular_content.startswith("gAAAAA"):
                    print("❌ Regular chat content is encrypted (should be plaintext)")
                    return False
                
                if regular_content != "This is a regular (non-secure) message":
                    print("❌ Regular chat content corrupted")
                    return False
            
            # Verify secure chat endpoints don't return regular chats
            try:
                from app.api.routes.secure_records import fetch_secure_chat
                fetch_secure_chat(regular_chat_id, self.test_user_id)
                print("❌ Secure endpoint returned regular chat (should fail)")
                return False
            except:
                print("✅ Secure endpoint correctly rejects regular chat")
            
            # Verify regular chat endpoints don't return secure chats
            try:
                fetch_chat(self.test_chat_id, self.test_user_id)
                print("❌ Regular endpoint returned secure chat (should fail)")
                return False
            except:
                print("✅ Regular endpoint correctly rejects secure chat")
            
            # Clean up regular chat
            self.db_collection.delete_one({"conversation_id": regular_chat_id})
            
            return True
            
        except Exception as e:
            print(f"❌ Regular vs secure separation test failed: {e}")
            return False
    
    def test_edge_cases(self) -> bool:
        """Test edge cases and error handling."""
        print("\n🧪 Testing edge cases...")
        
        try:
            # Test empty message encryption
            from app.utils.encryption import encrypt, decrypt
            
            empty_encrypted = encrypt("")
            empty_decrypted = decrypt(empty_encrypted)
            
            if empty_decrypted != "":
                print("❌ Empty message encryption/decryption failed")
                return False
            
            # Test very long message
            long_message = "A" * 10000
            long_encrypted = encrypt(long_message)
            long_decrypted = decrypt(long_encrypted)
            
            if long_decrypted != long_message:
                print("❌ Long message encryption/decryption failed")
                return False
            
            # Test special characters
            special_message = "àáâãäåæçèéêë 中文 🔒🛡️ \n\t\r"
            special_encrypted = encrypt(special_message)
            special_decrypted = decrypt(special_encrypted)
            
            if special_decrypted != special_message:
                print("❌ Special characters encryption/decryption failed")
                return False
            
            print("✅ All edge cases passed")
            return True
            
        except Exception as e:
            print(f"❌ Edge cases test failed: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all integration tests."""
        print("🚀 Starting SecureChat Integration Tests")
        print("=" * 60)
        
        # Setup
        if not self.setup_database():
            return False
        
        # Clean up any existing test data
        self.cleanup_test_data()
        
        success = True
        
        try:
            # Run tests in sequence
            tests = [
                ("Secure Chat Creation", self.test_secure_chat_creation()),
                ("Multiple Message Encryption", self.test_multiple_message_encryption()),
                ("Secure Chat Retrieval", self.test_secure_chat_retrieval()),
                ("Regular vs Secure Separation", self.test_regular_vs_secure_separation()),
                ("Edge Cases", self.test_edge_cases()),
            ]
            
            for test_name, test_coro in tests:
                try:
                    if asyncio.iscoroutine(test_coro):
                        result = await test_coro
                    else:
                        result = test_coro
                    
                    if not result:
                        print(f"\n❌ {test_name} FAILED")
                        success = False
                    else:
                        print(f"\n✅ {test_name} PASSED")
                        
                except Exception as e:
                    print(f"\n❌ {test_name} FAILED with exception: {e}")
                    success = False
            
        finally:
            # Cleanup
            self.cleanup_test_data()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 ALL INTEGRATION TESTS PASSED!")
            print("\nVerified:")
            print("✅ MongoDB never stores plaintext for secure chats")
            print("✅ Encryption/decryption works correctly")
            print("✅ End-to-end user scenarios work")
            print("✅ Regular and secure chats are properly separated")
            print("✅ Edge cases and error handling work")
        else:
            print("❌ SOME TESTS FAILED!")
            print("Check the output above for details.")
        
        return success

async def main():
    """Run the integration tests."""
    test_runner = SecureChatIntegrationTest()
    success = await test_runner.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
