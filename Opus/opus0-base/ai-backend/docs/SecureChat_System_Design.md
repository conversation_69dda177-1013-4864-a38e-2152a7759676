
# System Design – SecureChat API (Minimal Encryption)

While working through the full end-to-end-encryption design, I realised it would delay release far beyond the current timeline.  
For the MVP we will ship a simpler “secure chat” that uses a single symmetric key held on the backend instead of E2EE.  
Messages are encrypted at rest in MongoDB and transparently decrypted when returned to the frontend.

---

## 1 · Goal

Provide a fast‑to‑ship “secure chat” mode that mirrors the current chat API but encrypts every stored message with a single symmetric key.  
Messages stay plaintext in‑memory during generation/streaming yet are cipher‑text in MongoDB and transparently decrypted before returning to the frontend.

---

## 2 · Why this simplified approach?

| Requirement                  | Full E2EE           | Single‑Key Backend Encryption (chosen) |
|-----------------------------|---------------------|----------------------------------------|
| Front‑end changes           | Major (client crypto) | Minor (route names only)              |
| Protects data at rest       | ✅                   | ✅                                      |
| Rotating / multi‑key        | ❌ complex           | Possible later (key version field)     |

The symmetric‑key design gives immediate at‑rest protection without touching the LLM pipeline or socket streaming logic.  
Also no need to deal with key storage for the user (local storage, passkeys and more...).

---

## 3 · Execution Flow

1. `socket: chat_created`  
   → seed `chat_histories` (`is_secure: true`)

2. `socket: secure_chat_message` (user)  
   → `encrypt(content)`  
   → push ciphertext to MongoDB

3. LLM streams AI chunks (plaintext RAM only)

4. `socket: store_secure_ai_message`  
   → `encrypt(ai_text)`  
   → update MongoDB

5. `REST: GET /secure_chats/{id}`  
   → fetch doc  
   → decrypt every `msg.content`  
   → return plaintext JSON to frontend

---

## 4 · Key Modules & Files

| File                                | Action                                                                      |
|-------------------------------------|------------------------------------------------------------------------------|
| `app/utils/encryption.py`           | new helper: `encrypt(text)`, `decrypt(token)` using `cryptography.Fernet`  |
| `app/core/config.py`                | add `CHAT_ENCRYPTION_KEY: str` + load from `.env`                           |
| `app/api/routes/secure_chat.py`     | socket endpoints mirroring `chat.py` but encrypt/decrypt                    |
| `app/api/routes/secure_records.py`  | REST endpoints mirroring `records.py` for secure chats                      |
| `app/main.py`                       | add socket handlers: `secure_chat_created`, `secure_chat_message`, `store_secure_ai_message` |
| `.env.example`                      | add `CHAT_ENCRYPTION_KEY=<generated>` + doc comment                         |
| Frontend                            | `fetchSecureChat`, socket event names in `useSocketChat.ts`                 |

---

## 5 · Step-by-Step Implementation

- **Config** – generate key:  
  ```bash
  python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
  ```  
  → place in `.env`

- **Encryption helper** – wrap `Fernet`; raise on missing key

- **Secure routes** – duplicate logic from `chat.py` / `records.py`; encrypt before insert, decrypt before return

- **Socket events** – register secure variants in `main.py` (set `is_secure: True`)

- **Frontend hooks** – add REST + socket wrappers; UI displays decrypted text normally

- **Unit tests** – confirm: plaintext ↔ encrypt ↔ decrypt round‑trip; MongoDB never stores plaintext

- **Docs** – `README` section on key generation & rotation procedure

---

## 6 · MVP Scope

- ✅ Text messages only (attachments untouched)  
- ✅ Single symmetric key, manual rotation  
- ❌ No client‑side crypto, no per‑user keys, no forward secrecy

---

## 7 · Deliverables

- Basic chat implementation working completely  
- Verified encrypted data at rest in MongoDB  
- Frontend able to read decrypted messages from the database for display
