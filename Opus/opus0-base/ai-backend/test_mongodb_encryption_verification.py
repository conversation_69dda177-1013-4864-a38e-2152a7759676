#!/usr/bin/env python3
"""
MongoDB Encryption Verification Test

This test specifically focuses on verifying that MongoDB never stores
plaintext content for secure chats by directly inspecting the database.
"""

import os
import sys
import time
import re
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

# Set test environment
os.environ["CHAT_ENCRYPTION_KEY"] = "ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg="

class MongoDBEncryptionVerifier:
    """Verifies that MongoDB never stores plaintext for secure chats."""
    
    def __init__(self):
        self.test_chat_id = f"mongodb_test_{int(time.time())}"
        self.test_user_id = "507f1f77bcf86cd799439011"
        self.sensitive_test_data = [
            "password123",
            "API_KEY=sk-1234567890abcdef",
            "Social Security Number: ***********",
            "Credit Card: 4532-1234-5678-9012",
            "Secret: The launch codes are 00000000",
            "Personal info: <PERSON>, born 1985-03-15",
            "Database connection: *********************************"
        ]
        self.db_collection = None
    
    def setup_database(self):
        """Initialize database connection."""
        try:
            from app.db import chat_histories_collection
            self.db_collection = chat_histories_collection
            print("✅ Database connection established")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
    
    def cleanup_test_data(self):
        """Clean up test data."""
        if self.db_collection:
            result = self.db_collection.delete_many({
                "conversation_id": {"$regex": "^mongodb_test_"}
            })
            print(f"🧹 Cleaned up {result.deleted_count} test documents")
    
    def create_secure_chat_with_sensitive_data(self):
        """Create a secure chat with sensitive test data."""
        print("\n📝 Creating secure chat with sensitive data...")
        
        try:
            from app.utils.encryption import encrypt
            from bson import ObjectId
            from datetime import datetime
            
            now = datetime.utcnow()
            
            # Create chat document with encrypted messages
            encrypted_messages = []
            for i, sensitive_data in enumerate(self.sensitive_test_data):
                encrypted_content = encrypt(sensitive_data)
                encrypted_messages.append({
                    "role": "user",
                    "message_id": f"sensitive_msg_{i:03d}",
                    "content": encrypted_content,
                    "attachments": [],
                    "message_mode": "chat",
                    "timestamp": now
                })
            
            # Insert chat document
            chat_doc = {
                "conversation_id": self.test_chat_id,
                "user_id": ObjectId(self.test_user_id),
                "title": "Sensitive Data Test Chat",
                "preview": "",
                "created_at": now,
                "updated_at": now,
                "last_message_at": now,
                "is_favorite": False,
                "is_task": False,
                "is_secure": True,
                "messages": encrypted_messages,
            }
            
            self.db_collection.insert_one(chat_doc)
            print(f"✅ Created secure chat with {len(self.sensitive_test_data)} sensitive messages")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create secure chat: {e}")
            return False
    
    def verify_no_plaintext_in_mongodb(self):
        """Verify that no plaintext sensitive data exists in MongoDB."""
        print("\n🔍 Verifying no plaintext in MongoDB...")
        
        try:
            # Get the raw document from MongoDB
            chat_doc = self.db_collection.find_one({"conversation_id": self.test_chat_id})
            
            if not chat_doc:
                print("❌ Test chat document not found")
                return False
            
            # Convert document to string for searching
            doc_str = str(chat_doc)
            
            # Check for each piece of sensitive data
            violations = []
            for sensitive_data in self.sensitive_test_data:
                if sensitive_data.lower() in doc_str.lower():
                    violations.append(sensitive_data)
            
            if violations:
                print("❌ SECURITY VIOLATION: Found plaintext sensitive data in MongoDB!")
                for violation in violations:
                    print(f"   - Found: {violation}")
                return False
            
            # Verify all message content is encrypted (starts with gAAAAA)
            messages = chat_doc.get("messages", [])
            for i, msg in enumerate(messages):
                content = msg.get("content", "")
                if not content.startswith("gAAAAA"):
                    print(f"❌ Message {i+1} content not encrypted: {content}")
                    return False
            
            print(f"✅ All {len(messages)} messages properly encrypted in MongoDB")
            print("✅ No plaintext sensitive data found in database")
            return True
            
        except Exception as e:
            print(f"❌ MongoDB verification failed: {e}")
            return False
    
    def verify_encryption_patterns(self):
        """Verify that encrypted content follows expected patterns."""
        print("\n🔐 Verifying encryption patterns...")
        
        try:
            chat_doc = self.db_collection.find_one({"conversation_id": self.test_chat_id})
            messages = chat_doc.get("messages", [])
            
            for i, msg in enumerate(messages):
                content = msg.get("content", "")
                
                # Check Fernet token format
                if not re.match(r'^gAAAAA[A-Za-z0-9+/]+=*$', content):
                    print(f"❌ Message {i+1} has invalid Fernet token format: {content}")
                    return False
                
                # Check minimum length (Fernet tokens are at least 60 chars)
                if len(content) < 60:
                    print(f"❌ Message {i+1} encrypted content too short: {len(content)} chars")
                    return False
                
                # Verify it's different from original
                original = self.sensitive_test_data[i]
                if content == original:
                    print(f"❌ Message {i+1} content not encrypted (matches original)")
                    return False
            
            print(f"✅ All {len(messages)} messages have valid encryption patterns")
            return True
            
        except Exception as e:
            print(f"❌ Encryption pattern verification failed: {e}")
            return False
    
    def verify_decryption_works(self):
        """Verify that encrypted content can be properly decrypted."""
        print("\n🔓 Verifying decryption works correctly...")
        
        try:
            from app.utils.encryption import decrypt
            
            chat_doc = self.db_collection.find_one({"conversation_id": self.test_chat_id})
            messages = chat_doc.get("messages", [])
            
            for i, msg in enumerate(messages):
                encrypted_content = msg.get("content", "")
                expected_plaintext = self.sensitive_test_data[i]
                
                # Decrypt the content
                decrypted_content = decrypt(encrypted_content)
                
                if decrypted_content != expected_plaintext:
                    print(f"❌ Message {i+1} decryption failed:")
                    print(f"   Expected: {expected_plaintext}")
                    print(f"   Got: {decrypted_content}")
                    return False
            
            print(f"✅ All {len(messages)} messages decrypt correctly")
            return True
            
        except Exception as e:
            print(f"❌ Decryption verification failed: {e}")
            return False
    
    def test_search_resistance(self):
        """Test that MongoDB queries cannot find sensitive data."""
        print("\n🔍 Testing search resistance...")
        
        try:
            # Try to find documents containing sensitive data
            for sensitive_data in self.sensitive_test_data:
                # Search in message content
                results = list(self.db_collection.find({
                    "messages.content": {"$regex": re.escape(sensitive_data), "$options": "i"}
                }))
                
                if results:
                    print(f"❌ Found documents containing plaintext: {sensitive_data}")
                    return False
                
                # Search in entire document
                results = list(self.db_collection.find({
                    "$text": {"$search": sensitive_data}
                }))
                
                # This should not find anything (no text index on encrypted content)
                # But even if it did, it shouldn't match our encrypted content
            
            print("✅ MongoDB queries cannot find sensitive plaintext data")
            return True
            
        except Exception as e:
            print(f"❌ Search resistance test failed: {e}")
            return False
    
    def run_verification(self):
        """Run complete MongoDB encryption verification."""
        print("🔒 MongoDB Encryption Verification Test")
        print("=" * 50)
        
        if not self.setup_database():
            return False
        
        # Clean up any existing test data
        self.cleanup_test_data()
        
        success = True
        
        try:
            tests = [
                ("Create Secure Chat", self.create_secure_chat_with_sensitive_data),
                ("Verify No Plaintext in MongoDB", self.verify_no_plaintext_in_mongodb),
                ("Verify Encryption Patterns", self.verify_encryption_patterns),
                ("Verify Decryption Works", self.verify_decryption_works),
                ("Test Search Resistance", self.test_search_resistance),
            ]
            
            for test_name, test_func in tests:
                try:
                    result = test_func()
                    if not result:
                        print(f"\n❌ {test_name} FAILED")
                        success = False
                    else:
                        print(f"\n✅ {test_name} PASSED")
                except Exception as e:
                    print(f"\n❌ {test_name} FAILED with exception: {e}")
                    success = False
        
        finally:
            # Cleanup
            self.cleanup_test_data()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 MONGODB ENCRYPTION VERIFICATION PASSED!")
            print("\nVerified:")
            print("✅ No sensitive plaintext data stored in MongoDB")
            print("✅ All content properly encrypted with Fernet")
            print("✅ Encrypted content follows expected patterns")
            print("✅ Decryption works correctly")
            print("✅ Database queries cannot find sensitive data")
            print("\n🛡️ Your SecureChat implementation is secure!")
        else:
            print("❌ MONGODB ENCRYPTION VERIFICATION FAILED!")
            print("🚨 SECURITY ISSUE: Review the failures above")
        
        return success

def main():
    """Run the MongoDB encryption verification."""
    verifier = MongoDBEncryptionVerifier()
    success = verifier.run_verification()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
