# utils/logging_config.py
import logging
import logging.config
import os
from pathlib import Path
from rich.logging import <PERSON>Hand<PERSON>

from app.utils.constants import get_user_log_dir
from app.utils.live_updates import current_chat_id, current_user_id

DATA_DIR = Path(
    os.getenv("OPUS_DATA_DIR", Path(__file__).resolve().parent.parent.parent / "data")
)
DATA_DIR.mkdir(parents=True, exist_ok=True)
LOG_FILE = DATA_DIR / "app.log"


class PerUserFileHandler(logging.FileHandler):
    """FileHandler that routes logs into per-user directories."""

    def __init__(self, filename: str, **kwargs):
        self.default_file = Path(filename)
        super().__init__(filename, **kwargs)

    def _resolve_path(self) -> Path:
        user_id = current_user_id.get("")
        chat_id = current_chat_id.get("")
        if not user_id:
            return self.default_file
        log_dir = get_user_log_dir(user_id)
        if chat_id:
            log_dir = log_dir / chat_id
        return log_dir / self.default_file.name

    def emit(self, record: logging.LogRecord) -> None:  # noqa: D401
        """Emit the record to the file determined by ``_resolve_path``."""
        self.baseFilename = str(self._resolve_path())
        Path(self.baseFilename).parent.mkdir(parents=True, exist_ok=True)
        super().emit(record)


def init_logging():
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "rich": {
                "format": "[%(levelname)s] %(message)s",  # Minimal format: only level and message
            },
            "minimal": {
                "format": "[%(levelname)s] %(message)s",
            },
            "analysis": {
                "format": "[%(asctime)s] %(agent_id)s %(model)s %(label)s: %(duration).4f%(unit)s",
            },
        },
        "handlers": {
            "console": {
                "class": "rich.logging.RichHandler",
                "formatter": "rich",
                "level": "DEBUG",
                "rich_tracebacks": True,
                "tracebacks_show_locals": True,
            },
            "file": {
                "()": "app.utils.logging_config.PerUserFileHandler",
                "formatter": "minimal",
                "level": "DEBUG",
                "filename": str(LOG_FILE),
            },
            "analysis_file": {
                "()": "app.utils.logging_config.PerUserFileHandler",
                "formatter": "analysis",
                "level": "INFO",
                "filename": str(DATA_DIR / "agent_analysis.log"),
            },
        },
        "root": {
            "handlers": ["console", "file"],
            "level": "INFO",
        },
        "loggers": {
            "app": {
                "handlers": ["console", "file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "pymongo": {
                "handlers": ["console", "file"],
                "level": "WARNING",
                "propagate": False,
            },
            "uvicorn": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "socketio": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "agent_analysis": {
                "handlers": ["analysis_file"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }
    logging.config.dictConfig(config)
