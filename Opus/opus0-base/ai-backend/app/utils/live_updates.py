# app/utils/live_updates.py
"""
Utility for streaming structured runtime events to the client over Socket.IO.

We lazily import `sio` from app.main *inside* emit_log() to avoid circular‑import
issues during application start‑up.
"""

import json
import datetime as _dt
from typing import Optional
import importlib
import contextvars


TOK_BEGIN = "[LOG_START]"
TOK_END   = "[LOG_END]"

# Context variable to keep track of the originating chat_id
current_chat_id: contextvars.ContextVar[str] = contextvars.ContextVar(
    "current_chat_id", default=""
)

# Context variable to keep track of the active user_id
current_user_id: contextvars.ContextVar[str] = contextvars.ContextVar(
    "current_user_id", default=""
)



def _iso_now() -> str:
    """Return current UTC time in ISO‑8601 format with Z‑suffix."""
    return _dt.datetime.utcnow().isoformat(timespec="milliseconds") + "Z"


def _get_sio():
    """
    Import app.main lazily and return the global `sio` AsyncServer.
    Avoids circular‑import problems at module load time.
    """
    main = importlib.import_module("app.main")
    return getattr(main, "sio")


def emit_log(
    *,
    event: str,
    subtask_id: str,
    subtask_desc: str,
    room: Optional[str] = None,
) -> None:
    """
    Stream one runtime‑event frame to the front‑end.

    Parameters
    ----------
    event : "subtask_started" | "subtask_done"
    subtask_id : str
    subtask_desc : str
    room : Optional[str]
        Specific Socket.IO room / SID (None → broadcast).
    """
    frame = {
        "event": event,
        "ts": _iso_now(),
        "subtask_id": subtask_id,
        "subtask_description": subtask_desc,
    }
    payload = {
        "chatId": current_chat_id.get(""),
        "data": f"{TOK_BEGIN}{json.dumps(frame)}{TOK_END}",
    }

    # send asynchronously, never block caller
    sio = _get_sio()
    sio.start_background_task(sio.emit, "chat_response", payload, room=room)
