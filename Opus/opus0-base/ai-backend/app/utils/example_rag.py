# app/utils/example_rag.py
"""
Module: example_rag.py

Utility helpers to build and query a FAISS index of example tasks and prompts.
Supports multiple datasets (task_planner and pdf_generator) with clean separation.
"""

from __future__ import annotations


import json
import logging
from pathlib import Path
from typing import List, Tu<PERSON>, Dict, Optional

from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS

from app.utils.constants import BASE_DIR, DATA_DIR


logger = logging.getLogger(__name__)

# Dataset configurations
_DATASET_CONFIGS = {
    "task_planner": {
        "data_path": BASE_DIR / "app" / "rag_data" / "task_planner_examples.jsonl",
        "index_path": DATA_DIR / "task_planner_index",
    },
    "pdf_generator": {
        "data_path": BASE_DIR / "app" / "rag_data" / "pdf_generator_examples.jsonl",
        "index_path": DATA_DIR / "pdf_generator_index",
    },
    "pdf_section": {
        "data_path": BASE_DIR / "app" / "rag_data" / "pdf_section_examples.jsonl",
        "index_path": DATA_DIR / "pdf_section_index",
    },
    "pdf_head": {
        "data_path": BASE_DIR / "app" / "rag_data" / "pdf_head_examples.jsonl",
        "index_path": DATA_DIR / "pdf_head_index",
    },
    "subtask_refiner": {
        "data_path": BASE_DIR / "app" / "rag_data" / "subtask_refiner_examples.jsonl",
        "index_path": DATA_DIR / "subtask_refiner_index",
    },
    "docx_generator": {
        "data_path": BASE_DIR / "app" / "rag_data" / "docx_generator_examples.jsonl",
        "index_path": DATA_DIR / "docx_generator_index",
    },
    "docx_section": {
        "data_path": BASE_DIR / "app" / "rag_data" / "docx_section_examples.jsonl",
        "index_path": DATA_DIR / "docx_section_index",
    },
    "slides_generator": {
        "data_path": BASE_DIR / "app" / "rag_data" / "slides_generator_examples.jsonl",
        "index_path": DATA_DIR / "slides_generator_index",
    },
    "slides_content": {
        "data_path": BASE_DIR / "app" / "rag_data" / "slides_content_examples.jsonl",
        "index_path": DATA_DIR / "slides_content_index",
    },
    "xlsx_generator": {
        "data_path": BASE_DIR / "app" / "rag_data" / "xlsx_generator_examples.jsonl",
        "index_path": DATA_DIR / "xlsx_generator_index",
    },
    "xlsx_section": {
        "data_path": BASE_DIR / "app" / "rag_data" / "xlsx_section_examples.jsonl",
        "index_path": DATA_DIR / "xlsx_section_index",
    },
    "pptx_code": {
        "data_path": BASE_DIR / "app" / "rag_data" / "pptx_code_examples.jsonl",
        "index_path": DATA_DIR / "pptx_code_index",
    },
}

# Cached indexes and examples for each dataset
_INDEXES: Dict[str, FAISS | None] = {}
_EXAMPLES: Dict[str, List[dict] | None] = {}


# ──────────────────────────────────────────────────
#                         INTERNAL HELPERS
# ──────────────────────────────────────────────────


def _load_examples(data_path: Path) -> List[dict]:
    """Return all JSON objects from the given JSONL file."""
    examples = []
    if not data_path.exists():
        return examples

    with data_path.open("r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:
                examples.append(json.loads(line))
    return examples


# ──────────────────────────────────────────────────
#                         PUBLIC API
# ──────────────────────────────────────────────────


def build_index(
    dataset_name: str = "task_planner",
    data_path: Optional[Path] = None,
    index_path: Optional[Path] = None,
) -> Tuple[FAISS, List[dict]]:
    """Build a FAISS index from the JSONL dataset and persist it."""
    if data_path is None:
        data_path = _DATASET_CONFIGS[dataset_name]["data_path"]
    if index_path is None:
        index_path = _DATASET_CONFIGS[dataset_name]["index_path"]

    examples = _load_examples(data_path)
    texts = []
    for ex in examples:
        text = ex.get("task") or ex.get("prompt") or ex.get("query") or ""
        texts.append(text)
    metadata = [{"idx": i} for i in range(len(examples))]
    embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
    index = FAISS.from_texts(
        texts=texts,
        embedding=embeddings,
        metadatas=metadata,
    )
    index.save_local(str(index_path))
    return index, examples


def load_index(
    dataset_name: str = "task_planner",
    data_path: Optional[Path] = None,
    index_path: Optional[Path] = None,
) -> Tuple[FAISS, List[dict]]:
    """Return the FAISS index and examples, rebuilding if necessary."""
    if data_path is None:
        data_path = _DATASET_CONFIGS[dataset_name]["data_path"]
    if index_path is None:
        index_path = _DATASET_CONFIGS[dataset_name]["index_path"]

    # Check cached values for this dataset
    if (
        dataset_name in _INDEXES
        and _INDEXES[dataset_name] is not None
        and dataset_name in _EXAMPLES
        and _EXAMPLES[dataset_name] is not None
    ):
        return _INDEXES[dataset_name], _EXAMPLES[dataset_name]

    rebuild = not index_path.exists()
    if not rebuild and data_path.exists():
        rebuild = index_path.stat().st_mtime < data_path.stat().st_mtime

    if rebuild:
        index, examples = build_index(dataset_name, data_path, index_path)
        _INDEXES[dataset_name] = index
        _EXAMPLES[dataset_name] = examples
    else:
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        # Allow deserialization of the pickled FAISS index as it was
        # generated locally and is therefore trusted.
        index = FAISS.load_local(
            str(index_path),
            embeddings,
            allow_dangerous_deserialization=True,
        )
        examples = _load_examples(data_path)
        _INDEXES[dataset_name] = index
        _EXAMPLES[dataset_name] = examples

    return _INDEXES[dataset_name], _EXAMPLES[dataset_name]


def retrieve_examples(
    query: str, k: int = 3, dataset_name: str = "task_planner"
) -> List[dict]:
    """Return the top *k* examples most similar to the query string."""
    index, examples = load_index(dataset_name)
    docs = index.similarity_search(query, k=k)
    results = []
    for doc in docs:
        idx = doc.metadata.get("idx")
        if idx is not None and 0 <= idx < len(examples):
            results.append(examples[idx])
    return results


def example_to_text(example: dict) -> str:
    """Return a readable text snippet for a single example."""
    prompt = example.get("task", "")
    resp = json.dumps(example.get("response", {}), ensure_ascii=False)
    return f"Task: {prompt}\nResponse: {resp}"


def pdf_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a PDF generator example in the original prompt format."""
    user_input = example.get("user_input", "")
    subtask_desc = example.get("subtask_desc", "")
    deps_info = example.get("deps_info", "")
    response = example.get("response", {})
    explanation = example.get("explanation", "")

    # Format the response as JSON with proper escaping for the prompt
    response_json = json.dumps(response, ensure_ascii=False, indent=2)
    # Replace single braces with double braces for prompt template
    response_json = response_json.replace("{", "{{").replace("}", "}}")

    formatted_example = f"""- **User Message:** "{user_input}"
- **Subtask Description:** "{subtask_desc}"
- **Additional Information:** "{deps_info}"
- **Expected Output:**
{response_json}"""

    if explanation:
        formatted_example += f"\n- Explanation: {explanation}"

    return formatted_example


def pdf_section_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a PDF section example in the original prompt format."""
    response = example.get("response", {})
    title = response.get("title", "")
    html_content = response.get("html_content", "")
    head_content = response.get("head_content", "")

    if head_content:
        # Format with head and output sections
        formatted_example = f"""## {title}

(given head:)

{head_content}

(output:)

{html_content}"""
    else:
        # Format with just the HTML content
        formatted_example = f"""## {title}

{html_content}"""

    return formatted_example


def pdf_head_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a PDF head example in the original prompt format."""
    response = example.get("response", {})
    title = response.get("title", "")
    head_content = response.get("head_content", "")

    # Format as the original head examples
    formatted_example = f"""#### {title}
{head_content}"""

    return formatted_example


def subtask_refiner_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a subtask refiner example in the original prompt format."""
    import json

    all_subtasks_data = example.get("all_subtasks_data", {})
    completed_subtask_desc = example.get("completed_subtask_desc", "")
    completed_subtask_output = example.get("completed_subtask_output", "")
    pending_subtask_desc = example.get("pending_subtask_desc", "")
    response = example.get("response", {})
    reasoning = example.get("reasoning", "")

    # Format as the original subtask refiner examples
    formatted_example = f"""
        **All Subtasks Data:**
        ```json
        {json.dumps(all_subtasks_data, indent=4, ensure_ascii=False)}
        ```

        **Completed Subtask Description:**
        "{completed_subtask_desc}"

        **Completed Subtask Output:**
        "{completed_subtask_output}"

        **Pending Subtask Description:**
        "{pending_subtask_desc}"

        **Possible Output:**:
        ```json
        {json.dumps(response, indent=4, ensure_ascii=False)}
        ```
        (Reason "Not to be added in the final output": {reasoning})"""

    return formatted_example


def docx_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a DOCX generator example in the original prompt format."""
    """Return a readable text snippet for an Excel generator example in the original prompt format."""
    user_input = example.get("user_input", "")
    subtask_desc = example.get("subtask_desc", "")
    deps_info = example.get("deps_info", "")
    response = example.get("response", {})
    explanation = example.get("explanation", "")

    # Format the response as JSON with proper escaping for the prompt
    response_json = json.dumps(response, ensure_ascii=False, indent=2)
    # Replace single braces with double braces for prompt template
    response_json = response_json.replace("{", "{{").replace("}", "}}")

    formatted_example = f"""
        - **User Message:** "{user_input}"
        - **Subtask Description:** "{subtask_desc}"
        - **Additional Information:** "{deps_info}"
        - **Expected Output:**
        {response_json}"""

    if explanation:
        formatted_example += f"\n- Explanation: {explanation}"

    return formatted_example


def xlsx_example_to_text(example: dict) -> str:
    """Return a readable text snippet for an Excel generator example in the original prompt format."""
    user_input = example.get("user_input", "")
    subtask_desc = example.get("subtask_desc", "")
    deps_info = example.get("deps_info", "")
    response = example.get("response", {})
    explanation = example.get("explanation", "")

    # Format the response as JSON with proper escaping for the prompt
    response_json = json.dumps(response, ensure_ascii=False, indent=2)
    # Replace single braces with double braces for prompt template
    response_json = response_json.replace("{", "{{").replace("}", "}}")

    formatted_example = f"""
        - **User Message:** "{user_input}"
        - **Subtask Description:** "{subtask_desc}"
        - **Additional Information:** "{deps_info}"
        - **Expected Output:**
        {response_json}"""

    if explanation:
        formatted_example += f"\n- Explanation: {explanation}"

    return formatted_example


def slides_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a slides generator example in the original prompt format."""
    user_input = example.get("user_input", "")
    subtask_desc = example.get("subtask_desc", "")
    deps_info = example.get("deps_info", "")
    response = example.get("response", {})
    explanation = example.get("explanation", "")

    # Format the response as JSON with proper escaping for the prompt
    response_json = json.dumps(response, ensure_ascii=False, indent=2)
    # Replace single braces with double braces for prompt template
    response_json = response_json.replace("{", "{{").replace("}", "}}")

    formatted_example = f"""
        - **User Message:** "{user_input}"
        - **Subtask Description:** "{subtask_desc}"
        - **Additional Information:** "{deps_info}"
        - **Expected Output:**
        {response_json}"""

    if explanation:
        formatted_example += f"\n- Explanation: {explanation}"

    return formatted_example


def docx_section_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a DOCX section example in Markdown format."""
    response = example.get("response", {})
    title = response.get("title", "")
    markdown_content = response.get("markdown_content", "")

    # Format with title and markdown content
    formatted_example = f"""## {title}

    {markdown_content}"""

    return formatted_example


def slides_content_example_to_text(example: dict) -> str:
    """Return a readable text snippet for a slides content example in the original prompt format."""
    response = example.get("response", "")

    # Format the markdown content directly
    formatted_example = f"""{response}"""

    return formatted_example


def xlsx_section_example_to_text(example: dict) -> str:
    """Return a readable text snippet for an Excel section example in the original prompt format."""
    response = example.get("response", {})
    worksheets = response.get("worksheets", [])

    if not worksheets:
        return "No worksheet data available"

    formatted_example = ""
    for worksheet in worksheets:
        worksheet_name = worksheet.get("name", "Sheet1")
        tables = worksheet.get("tables", [])

        formatted_example += f"## {worksheet_name}\n\n"

        for table in tables:
            headers = table.get("headers", [])
            rows = table.get("rows", [])

            if headers:
                formatted_example += (
                    "Headers: "
                    + ", ".join([h.get("value", "") for h in headers])
                    + "\n"
                )

            if rows:
                formatted_example += f"Sample data ({len(rows)} rows):\n"
                for i, row in enumerate(rows[:3]):  # Show first 3 rows
                    row_values = [str(cell.get("value", "")) for cell in row]
                    formatted_example += f"Row {i+1}: " + ", ".join(row_values) + "\n"

            formatted_example += "\n"

    return formatted_example.strip()


def pptx_code_example_to_text(example: dict) -> str:
    """Format python-pptx code examples for prompts."""
    task = example.get("task", "")
    code = example.get("code", "")
    explanation = example.get("explanation", "")

    return f"""**Task:** {task}
**Code:**
```python
{code}
```
**Explanation:** {explanation}"""


def get_examples_text(
    query: str, k: int = 3, dataset_name: str = "task_planner"
) -> str:
    """Retrieve and format the top *k* examples matching the query."""
    results = retrieve_examples(query, k=k, dataset_name=dataset_name)
    if dataset_name == "pdf_generator":
        return "\n\n".join(pdf_example_to_text(ex) for ex in results)
    elif dataset_name == "pdf_section":
        return "\n\n".join(pdf_section_example_to_text(ex) for ex in results)
    elif dataset_name == "pdf_head":
        return "\n\n".join(pdf_head_example_to_text(ex) for ex in results)
    elif dataset_name == "subtask_refiner":
        return "\n\n---\n\n".join(subtask_refiner_example_to_text(ex) for ex in results)
    elif dataset_name == "docx_generator":
        return "\n\n".join(docx_example_to_text(ex) for ex in results)
    elif dataset_name == "docx_section":
        return "\n\n".join(docx_section_example_to_text(ex) for ex in results)
    elif dataset_name == "slides_generator":
        return "\n\n".join(slides_example_to_text(ex) for ex in results)
    elif dataset_name == "slides_content":
        return "\n\n".join(slides_content_example_to_text(ex) for ex in results)
    elif dataset_name == "xlsx_generator":
        return "\n\n".join(xlsx_example_to_text(ex) for ex in results)
    elif dataset_name == "xlsx_section":
        return "\n\n".join(xlsx_section_example_to_text(ex) for ex in results)
    elif dataset_name == "pptx_code":
        return "\n\n".join(pptx_code_example_to_text(ex) for ex in results)
    else:
        return "\n\n".join(example_to_text(ex) for ex in results)


def add_example(example: dict, dataset_name: str = "task_planner") -> None:
    """Append a new example to the dataset and rebuild the index."""
    data_path = _DATASET_CONFIGS[dataset_name]["data_path"]
    index_path = _DATASET_CONFIGS[dataset_name]["index_path"]

    with data_path.open("a", encoding="utf-8") as f:
        f.write(json.dumps(example, ensure_ascii=False) + "\n")
    build_index(dataset_name, data_path, index_path)
    # Reset cached index for this dataset
    _INDEXES[dataset_name] = None
    _EXAMPLES[dataset_name] = None


# ──────────────────────────────────────────────────
#                         CONVENIENCE FUNCTIONS
# ──────────────────────────────────────────────────


def get_pdf_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get PDF generator examples."""
    results = retrieve_examples(query, k=k, dataset_name="pdf_generator")
    formatted_examples = []
    for i, ex in enumerate(results, 1):
        formatted_example = (
            f"### Example {i}: {ex.get('response', {}).get('title', 'PDF Outline')}\n"
        )
        formatted_example += pdf_example_to_text(ex)
        formatted_examples.append(formatted_example)
    return "\n\n".join(formatted_examples)


def get_pdf_section_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get PDF section examples."""
    return get_examples_text(query, k=k, dataset_name="pdf_section")


def get_pdf_head_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get PDF head examples."""
    return get_examples_text(query, k=k, dataset_name="pdf_head")


def get_subtask_refiner_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get subtask refiner examples."""
    return get_examples_text(query, k=k, dataset_name="subtask_refiner")


def get_task_planner_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get task planner examples."""
    return get_examples_text(query, k=k, dataset_name="task_planner")


def get_docx_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get DOCX generator examples."""
    results = retrieve_examples(query, k=k, dataset_name="docx_generator")
    formatted_examples = []
    for i, ex in enumerate(results, 1):
        formatted_example = (
            f"### Example {i}: {ex.get('response', {}).get('title', 'DOCX Outline')}\n"
        )
        formatted_example += docx_example_to_text(ex)
        formatted_examples.append(formatted_example)
    return "\n\n".join(formatted_examples)


def get_slides_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get slides generator examples."""
    results = retrieve_examples(query, k=k, dataset_name="slides_generator")
    formatted_examples = []
    for i, ex in enumerate(results, 1):
        formatted_example = f"### Example {i}: {ex.get('response', {}).get('title', 'Slides Outline')}\n"
        formatted_example += slides_example_to_text(ex)
        formatted_examples.append(formatted_example)
    return "\n\n".join(formatted_examples)


def get_xlsx_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get Excel generator examples."""
    results = retrieve_examples(query, k=k, dataset_name="xlsx_generator")
    formatted_examples = []
    for i, ex in enumerate(results, 1):
        formatted_example = (
            f"### Example {i}: {ex.get('response', {}).get('title', 'Excel Outline')}\n"
        )
        formatted_example += xlsx_example_to_text(ex)
        formatted_examples.append(formatted_example)
    return "\n\n".join(formatted_examples)


def get_docx_section_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get DOCX section examples."""
    return get_examples_text(query, k=k, dataset_name="docx_section")


def get_slides_content_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get slides content examples."""
    return get_examples_text(query, k=k, dataset_name="slides_content")


def get_xlsx_section_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get Excel section examples."""
    return get_examples_text(query, k=k, dataset_name="xlsx_section")


def get_pptx_code_examples_text(query: str, k: int = 3) -> str:
    """Convenience function to get python-pptx code examples."""
    return get_examples_text(query, k=k, dataset_name="pptx_code")
