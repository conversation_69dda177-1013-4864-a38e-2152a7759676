# app/utils/encryption.py
"""
Encryption utilities for SecureChat using Fernet symmetric encryption.
Provides encrypt/decrypt functions for message content at rest.
"""

import logging
from cryptography.fernet import Fernet
from app.core.config import settings

logger = logging.getLogger(__name__)


class EncryptionError(Exception):
    """Raised when encryption/decryption operations fail."""
    pass


def _get_fernet() -> <PERSON><PERSON><PERSON>:
    """Get Fernet instance with the configured encryption key."""
    if not settings.CHAT_ENCRYPTION_KEY:
        raise EncryptionError("CHAT_ENCRYPTION_KEY not configured in environment")
    
    try:
        key = settings.CHAT_ENCRYPTION_KEY.encode('utf-8')
        return Fernet(key)
    except Exception as e:
        raise EncryptionError(f"Failed to initialize Fernet cipher: {e}")


def encrypt(plaintext: str) -> str:
    """
    Encrypt plaintext message content.
    
    Args:
        plaintext: The message content to encrypt
        
    Returns:
        Base64-encoded encrypted token as string
        
    Raises:
        EncryptionError: If encryption fails
    """
    if not plaintext:
        return plaintext
    
    try:
        fernet = _get_fernet()
        encrypted_bytes = fernet.encrypt(plaintext.encode('utf-8'))
        return encrypted_bytes.decode('utf-8')
    except Exception as e:
        logger.error(f"Encryption failed: {e}")
        raise EncryptionError(f"Failed to encrypt message: {e}")


def decrypt(encrypted_token: str) -> str:
    """
    Decrypt encrypted message content.
    
    Args:
        encrypted_token: Base64-encoded encrypted token
        
    Returns:
        Decrypted plaintext message
        
    Raises:
        EncryptionError: If decryption fails
    """
    if not encrypted_token:
        return encrypted_token
    
    try:
        fernet = _get_fernet()
        decrypted_bytes = fernet.decrypt(encrypted_token.encode('utf-8'))
        return decrypted_bytes.decode('utf-8')
    except Exception as e:
        logger.error(f"Decryption failed: {e}")
        raise EncryptionError(f"Failed to decrypt message: {e}")
