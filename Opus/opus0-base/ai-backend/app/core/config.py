# app/core/config.py
import os
from pathlib import Path
from pydantic import AnyHttpUrl
from pydantic_settings import BaseSettings
from typing import ClassVar, List, Optional
from dotenv import load_dotenv

load_dotenv()  # Force reload .env to ensure all environment variables are captured

class Settings(BaseSettings):
    OPENAI_API_KEY: str
    FRONTEND_URL: str
    PORT: int = 8000
    DEBUG: bool = False
    CORS_ORIGINS: List[str] = []
    MONGODB_USERNAME: str
    MONGODB_PASSWORD: str
    MONGODB_HOST: str
    MONGODB_DATABASE: str
    ANTHROPIC_API_KEY: Optional[str]  # Added for Anthropic API key
    GOOGLE_API_KEY: Optional[str]     # Added for Google API key
    E2B_API_KEY: Optional[str]
    TAVILY_API_KEY: Optional[str]
    SCRAPER: Optional[str]
    RETRIEVER: Optional[str]
    BRAVE_SEARCH_API: Optional[str]    # Added for Brave Search API key
    DEEPSEEK_API_KEY: Optional[str] = None    # Added for DeepSeek API key
    MISTRAL_API_KEY: str                     # for Document-AI OCR

    # SecureChat encryption key
    CHAT_ENCRYPTION_KEY: Optional[str] = None  # Fernet key for message encryption
    # Cloudflare R2 configuration
    R2_ACCOUNT_ID: str
    R2_ENDPOINT: AnyHttpUrl            # e.g. https://<account_id>.r2.cloudflarestorage.com
    R2_BUCKET_NAME: str                # e.g. opus0-uploads
    R2_ACCESS_KEY_ID: str              # from R2 “Manage R2 API Tokens”
    R2_SECRET_ACCESS_KEY: str          # from R2 “Manage R2 API Tokens”

    API_URL: AnyHttpUrl = "http://localhost:8000"
    PUBLIC_PDF_DIR: str = "public_pdfs"
    USER_UPLOADS_DIR: str = "user_uploads"
    ACCESS_CODE: str = "project2501"
    
    GOOGLE_OAUTH_CLIENT_ID: Optional[str] = None
    GOOGLE_OAUTH_CLIENT_SECRET: Optional[str] = None
    
    # these three need annotations so Pydantic doesn’t treat them as missing-type fields
    SERVER_HOST: ClassVar[str] = os.getenv("SERVER_HOST", "localhost")
    SERVER_PORT: ClassVar[str] = os.getenv("SERVER_PORT", "8000")
    BASE_URL:   ClassVar[str] = f"http://{SERVER_HOST}:{SERVER_PORT}"

    

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"    # ← allow undeclared env-vars without error



    def model_post_init(self, __context):
        if self.FRONTEND_URL and self.FRONTEND_URL not in self.CORS_ORIGINS:
            self.CORS_ORIGINS.append(self.FRONTEND_URL)

settings = Settings()