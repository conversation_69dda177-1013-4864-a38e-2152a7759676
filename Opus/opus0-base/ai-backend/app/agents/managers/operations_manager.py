# app/agents/managers/operations_manager.py
"""
Module: operations_manager.py

This module defines the OperationsManagerAgent class, which is responsible for managing and assigning subtasks
to worker agents. It monitors task progress by reading and updating the task_status.json file, handles dependency
checks, gathers dependency outputs, assigns tasks to worker agents, processes messages from worker agents, and
updates the overall task status accordingly. The async loop ensures all subtasks are completed before signaling
termination.
"""

import json
import os
import logging
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
from app.core.config import settings

from app.utils.task_status import (
    read_task_status,
    update_subtask_status,
    update_task_status,
    get_subtask_uploads,
)
from app.utils.communication import send_message, receive_message
from app.agents.base_agent import BaseAgent
from app.utils.constants import SLEEP_INTERVAL, get_user_uploads_dir
from app.utils.live_updates import emit_log  # NEW

# Import the subtask refiner utility function
from app.utils.subtask_refiner import refine_dependent_subtasks


# Helper to truncate long strings in logs
def summarize(data: str, max_length: int = 100) -> str:
    return data if len(data) <= max_length else data[:max_length] + "..."


class OperationsManagerAgent(BaseAgent):
    """
    Operations Manager Agent that manages and assigns subtasks to worker agents.
    """

    # ──────────────────────────────────────────────────────────────
    #                         INITIALISATION
    # ──────────────────────────────────────────────────────────────
    def __init__(
        self,
        agent_id: str = "manager",
        done_event: Optional[asyncio.Event] = None,
        chat_history: Optional[List[Dict[str, Any]]] = None,
        chat_id: str = "",
    ):
        super().__init__(agent_id, agent_type="operations_manager")
        self.done_event = done_event
        self.chat_history = chat_history or []
        self.chat_id = chat_id
        self.task_status = self.load_task_status()

    # ──────────────────────────────────────────────────────────────
    #                       INTERNAL HELPERS
    # ──────────────────────────────────────────────────────────────
    def load_task_status(self) -> Dict[str, Any]:
        """Read the latest task_status.json from disk."""
        return read_task_status(self.chat_id)

    def extract_clean_history(self) -> str:
        """
        Convert self.history (list of {role,content}) into a single string
        containing only 'user: ...' and 'assistant: ...' lines.
        """
        raw = getattr(self, "history", [])
        lines: List[str] = []
        for msg in raw:
            if msg.get("role") in ("user", "assistant"):
                lines.append(f"{msg['role']}: {msg['content']}")
        return "\n".join(lines)

    # ---------- dependant-discovery & refiner-gate ---------- #
    def _get_dependent_subtasks(
        self, completed_id: str, ts: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Return all *pending* or *in_progress* subtasks whose deps contain `completed_id`.
        """
        subtasks = ts.get("subtasks", {}).get("subtasks", [])
        return [
            st
            for st in subtasks
            if st["status"] == "pending" and completed_id in st.get("deps", [])
        ]

    def _should_run_refiner(self, completed_id: str, ts: Dict[str, Any]) -> bool:
        """
        Decide whether to invoke the subtask refiner for `completed_id`.

        Logic:
        • If no dependants → False.
        • For each dependant:
          – Remove `completed_id` from its deps → other_deps.
          – If other_deps empty → OK.
          – Else all other_deps **must** already be `completed`.
        • If every dependant passes → True, else False.
        """
        dependants = self._get_dependent_subtasks(completed_id, ts)
        if not dependants:
            return False

        subtasks = ts.get("subtasks", {}).get("subtasks", [])
        status_lookup = {st["id"]: st["status"] for st in subtasks}

        # As soon as any dependant has no other_deps or all its other_deps completed, we run the refiner
        for st in dependants:
            other_deps = [d for d in st.get("deps", []) if d != completed_id]
            if not other_deps or all(
                status_lookup.get(d) == "completed" for d in other_deps
            ):
                self.logger.info(
                    f"Refiner gate: dependant '{st['id']}' is ready (other_deps={other_deps}). "
                    "Will run refiner."
                )
                return True

        # No dependant was ready yet
        self.logger.info(
            f"Refiner gate: no dependants of {completed_id} are ready. "
            f"Pending deps for: {[st['id'] for st in dependants]}"
        )
        return False

    # ──────────────────────────────────────────────────────────────
    #                    SUBTASK ASSIGNMENT LOGIC
    # ──────────────────────────────────────────────────────────────
    def all_deps_completed(
        self, subtask: Dict[str, Any], subtasks: List[Dict[str, Any]]
    ) -> bool:
        deps = subtask.get("deps", [])
        if not deps:
            return True
        status_lookup = {st["id"]: st["status"] for st in subtasks}
        return all(status_lookup.get(dep_id) == "completed" for dep_id in deps)

    def gather_dependency_outputs(
        self, subtask: Dict[str, Any], subtasks: List[Dict[str, Any]]
    ) -> str:
        deps_info: List[str] = []
        deps = [d for d in (subtask.get("deps") or []) if d]
        uploads = [u for u in (subtask.get("uploads") or []) if u]
        self.logger.debug("gather_dependency_outputs deps=%s uploads=%s", deps, uploads)

        file_lookup = {
            st["id"]: st.get("output_file") for st in subtasks if st.get("output_file")
        }

        # 1️⃣ pull in uploaded-docs before any deps
        if uploads:
            docs_dir = get_user_uploads_dir(self.chat_id)
            uploaded_texts = [
                f"# Uploaded Document: {fn}\n{(docs_dir / fn).read_text(encoding='utf-8')}"
                for fn in uploads
                if (docs_dir / fn).is_file() and fn.lower().endswith(".md")
            ]
            deps_info.extend(uploaded_texts)

        for dep_id in deps:
            dep_file = file_lookup.get(dep_id)
            if dep_file and not dep_file.startswith("./app/knowledge_base/"):
                dep_file = os.path.join("./app/knowledge_base/", dep_file)
            self.logger.info(
                f"Gathering dependency output for dep_id: {dep_id} from file: {dep_file}"
            )
            if dep_file and os.path.isfile(dep_file):
                with open(dep_file, "r", encoding="utf-8") as f:
                    content = f.read()
                self.logger.info(
                    f"Read {len(content)} characters from dependency file for dep_id: {dep_id}"
                )
                deps_info.append(f"Dep {dep_id}: {content}")
            else:
                self.logger.warning(
                    f"Dependency file for dep_id {dep_id} not found or inaccessible."
                )
        self.logger.debug("Deps info raw list: %s", deps_info)
        clean_info = [text for text in deps_info if text]
        return " | ".join(clean_info)

    def assign_tasks(self, completed_subtask_ids: Optional[List[str]] = None):
        self.logger.info("Assign tasks")
        self.task_status = self.load_task_status()

        subtasks = self.task_status.get("subtasks", {}).get("subtasks", [])
        self.logger.info(f"Total subtasks: {len(subtasks)}")

        assignable = [
            st
            for st in subtasks
            if st["status"] == "pending" and self.all_deps_completed(st, subtasks)
        ]

        self.logger.info(
            f"{len(assignable)} tasks ready" if assignable else "No tasks ready"
        )

        for subtask in assignable:
            self.logger.info(
                f"Assign subtask {subtask['id']} - {summarize(subtask.get('desc', ''))}"
            )
            try:
                criteria = self.task_status["subtasks"]["criteria"]
                deps_info = self.gather_dependency_outputs(subtask, subtasks)

                # DEBUG: show exactly what this subtask dict holds
                self.logger.debug(
                    "[Mgr] this subtask payload:\n%s", json.dumps(subtask, indent=2)
                )

                # build full task‐details payload including files/images & history
                # pull uploads directly from the canonical task_status.json
                uploads = get_subtask_uploads(self.chat_id, subtask["id"])
                self.logger.debug(
                    f"[Mgr→Wkr] subtask={subtask['id']} uploads={uploads}"
                )
                # 1) turn self.chat_history (list of dicts) into a single string
                history_lines = []
                for msg in self.chat_history:
                    # expect msg["role"] in {"user","assistant"}
                    role = msg.get("role", "unknown")
                    content = msg.get("content")
                    if content is None:
                        self.logger.debug(f"History message missing content: {msg}")
                        content = ""
                    history_lines.append(f"{role}: {content.strip()}")
                history_str = "\n".join(history_lines)
                task_details = {
                    "subtask_id": subtask["id"],
                    "subtask_description": subtask["desc"],
                    "user_message": criteria,
                    "deps_info": deps_info,
                    # now guaranteed correct
                    "uploaded_images": uploads,
                    # include the full “role: content” history string
                    "history": history_str,
                    # full task status for router context
                    "task_status": json.dumps(self.task_status.get("subtasks", {})),
                }

                # DEBUG: show exactly what we’re about to send
                self.logger.debug(
                    f"[Mgr→Wkr] full uploaded images: {json.dumps(task_details, indent=2)}"
                )
                send_message(
                    self.chat_id,
                    sender="manager",
                    receiver="worker",
                    message=task_details,
                )

                emit_log(
                    event="subtask_started",
                    subtask_id=subtask["id"],
                    subtask_desc=subtask["desc"],
                )
                update_subtask_status(
                    self.chat_id, subtask_id=subtask["id"], new_status="in_progress"
                )

            except Exception as e:
                self.logger.error(f"Subtask {subtask['id']} error: {e}")

    # ──────────────────────────────────────────────────────────────
    #                   REFINER AND MESSAGE HANDLING
    # ──────────────────────────────────────────────────────────────
    async def log_dependent_pending_subtasks(self, completed_subtask_id: str):
        await refine_dependent_subtasks(
            self.chat_id,
            self.load_task_status(),
            completed_subtask_id,
            self.logger,
        )

    async def process_messages(self) -> List[str]:
        self.logger.info("Process messages")
        completed_subtask_ids: List[str] = []

        while True:
            message = receive_message(self.chat_id, agent_id=self.agent_id)
            if not message:
                break

            subtask_id = message.get("subtask_id")
            output_file = message.get("output_file")

            if subtask_id and output_file:
                self.logger.info(
                    f"Worker reported subtask {subtask_id} done with output_file: {output_file}"
                )

                # 1️⃣  Write output_file path
                task_status = self.load_task_status()
                for st in task_status["subtasks"]["subtasks"]:
                    if st["id"] == subtask_id:
                        st["output_file"] = output_file
                        break
                update_task_status(self.chat_id, task_status["subtasks"])
                task_status = self.load_task_status()

                # 2️⃣  Gate refiner invocation
                if self._should_run_refiner(subtask_id, task_status):
                    self.logger.info(
                        f"Refiner conditions met for {subtask_id}. Running refiner."
                    )
                    await self.log_dependent_pending_subtasks(subtask_id)
                    # ── reload after refiner may have rewritten subtasks ──
                    task_status = self.load_task_status()
                else:
                    self.logger.info(f"Refiner skipped for {subtask_id}.")

                # 3️⃣  Mark completed
                update_subtask_status(
                    self.chat_id, subtask_id=subtask_id, new_status="completed"
                )
                # ── reload so any downstream logic sees the new status immediately ──
                task_status = self.load_task_status()

                desc = next(
                    (
                        st["desc"]
                        for st in task_status["subtasks"]["subtasks"]
                        if st["id"] == subtask_id
                    ),
                    "",
                )
                emit_log(event="subtask_done", subtask_id=subtask_id, subtask_desc=desc)

                completed_subtask_ids.append(subtask_id)

        return completed_subtask_ids

    # ──────────────────────────────────────────────────────────────
    #                    LEGACY SYNC ENTRYPOINT
    # ──────────────────────────────────────────────────────────────
    def execute_task(self, task_details: Dict[str, Any]):
        self.logger.info("Ops Manager triggered for legacy. No need for it here")

    # ──────────────────────────────────────────────────────────────
    #                           MAIN LOOP
    # ──────────────────────────────────────────────────────────────
    async def run_async(self):

        self.logger.info("Ops Manager async start")
        while True:
            try:
                completed_ids = await self.process_messages()
                self.assign_tasks(completed_subtask_ids=completed_ids)

                self.task_status = self.load_task_status()
                all_subs = self.task_status.get("subtasks", {}).get("subtasks", [])
                if all_subs and all(st["status"] == "completed" for st in all_subs):
                    self.logger.info("All tasks complete. Exiting loop.")
                    if self.done_event:
                        self.done_event.set()
                    break

            except Exception:
                self.logger.exception("Loop error")
            finally:
                await asyncio.sleep(SLEEP_INTERVAL)
