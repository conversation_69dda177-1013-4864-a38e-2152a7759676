# app/main.py

from app.utils.constants import (
    SESSIONS_ROOT,
    get_knowledge_base_dir,
    get_agent_comm_dir,
    get_public_pdf_dir,
    get_user_uploads_dir,
    get_user_log_dir,
    get_chat_log_dir,
)

# Import and initialize centralized logging config
from app.utils.logging_config import init_logging

init_logging()
import logging

logger = logging.getLogger(__name__)

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
import shutil
import asyncio
from pathlib import Path
from fastapi.middleware.cors import CORSMiddleware
from botocore.exceptions import ClientError
import socketio
from app.api.routes import command, chat
from app.utils.live_updates import current_chat_id, current_user_id
from app.utils.clear_data import clear_directory
from app.api.routes.files import router as files_router
from app.api.routes.uploads import router as uploads_router
from app.api.routes.auth import router as auth_router
from app.api.routes.docs import router as docs_router
from app.api.routes.records import router as records_router
from app.api.routes.secure_records import router as secure_records_router
from app.core.config import settings
from bson import ObjectId
from app.db import (
    ADMIN_USER_ID,
    chat_histories_collection,
    users_collection,
)

app = FastAPI()

from app.core.config import settings
import boto3
from pathlib import Path
from datetime import datetime

from app.utils.document_reader import ocr_document_to_markdown
from app.utils.token_price_calculator import calculate_costs, tally_tokens
from urllib.parse import urlparse


# Ensure the sessions root exists so we can store per-chat data
SESSIONS_ROOT.mkdir(parents=True, exist_ok=True)

# Serve session files (including uploads and PDFs) at /sessions/…
app.mount(
    "/sessions",
    StaticFiles(directory=str(SESSIONS_ROOT)),
    name="sessions",
)


# helper to fetch & save an image locally
async def save_user_image(client, key: str, name: str, chat_id: str) -> Path:
    up = get_user_uploads_dir(chat_id)
    up.mkdir(parents=True, exist_ok=True)
    dest = up / name
    obj = client.get_object(Bucket=settings.R2_BUCKET_NAME, Key=key)
    dest.write_bytes(obj["Body"].read())
    return dest


# helper: read all .md uploads into one big string
def collect_uploaded_docs(chat_id: str) -> str:
    up = get_user_uploads_dir(chat_id)
    md_files = sorted(up.glob("*.md"))
    sections = [f"---\n# {md.name}\n{md.read_text()}" for md in md_files]
    return "\n\n".join(sections)


# helper: list all uploaded images
def collect_uploaded_images(chat_id: str) -> list[str]:
    # return full paths so TaskPlanner can locate them
    up = get_user_uploads_dir(chat_id)
    return [str(img.resolve()) for img in sorted(up.glob("upload_image_*.*"))]


# Now it’s safe to mount
# app.mount("/pdfs", StaticFiles(directory=str(PUBLIC_PDF_PATH)), name="pdfs")

# mount our file-serving endpoint
app.include_router(files_router)
app.include_router(uploads_router)
app.include_router(auth_router)
app.include_router(docs_router)
app.include_router(records_router)
app.include_router(secure_records_router)


app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin) for origin in settings.CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

sio = socketio.AsyncServer(async_mode="asgi", cors_allowed_origins="*")
socket_app = socketio.ASGIApp(sio, app)

# Log the database connection status
logger.info("Database connection is established.")


@sio.on("connect")
async def connect(sid, environ):
    logger.info(f"Client connected: {sid}")


@sio.on("disconnect")
async def disconnect(sid):
    logger.info(f"Client disconnected: {sid}")


@sio.on("chat_created")
async def new_chat(sid, data):
    """Seed a fresh chat history document for this chatId."""
    chat_id = data.get("chatId")
    user_id = data.get("userId")
    if not chat_id:
        return
    now = datetime.utcnow()
    doc = {
        "conversation_id": chat_id,
        "user_id": ObjectId(user_id) if user_id else ADMIN_USER_ID,
        "title": "Untitled Chat",
        "preview": "",
        "created_at": now,
        "updated_at": now,
        "last_message_at": None,
        "is_favorite": False,
        "is_task": False,
        "messages": [],
    }
    token = current_user_id.set(user_id or "")
    chat_histories_collection.insert_one(doc)

    if user_id:
        get_chat_log_dir(user_id, chat_id)

    # prepare session directories
    kb_dir = get_knowledge_base_dir(chat_id)
    comm_dir = get_agent_comm_dir(chat_id)
    pdf_dir = get_public_pdf_dir(chat_id)
    uploads_dir = get_user_uploads_dir(chat_id)
    clear_directory(kb_dir)
    clear_directory(comm_dir)
    clear_directory(pdf_dir)
    clear_directory(uploads_dir)

    logger.info(f"Created chat history for {chat_id}")
    current_user_id.reset(token)


@sio.on("chat_update")
async def chat_update(sid, data):
    """Update chat document with arbitrary fields."""
    chat_id = data.get("chatId")
    updates = data.get("updates", {})
    if not chat_id or not isinstance(updates, dict):
        return

    now = datetime.utcnow()
    chat_histories_collection.update_one(
        {"conversation_id": chat_id},
        {"$set": {**updates, "updated_at": now}},
    )
    logger.info(f"Chat {chat_id} updated: {list(updates.keys())}")


@sio.on("chat_message")
async def handle_message(sid, data):
    """
    data = {
      "chatId": ...,
      "messageId": ...,
      "content": ...,
      "messageMode": ...
    }
    """

    content = data["content"]
    # take every attachment, but skip ones not yet uploaded (empty key)
    attachments = data.get("attachments", [])
    chat_id = data["chatId"]  # e.g. "chat_174558…"
    message_id = data.get("messageId")
    message_mode = data.get("messageMode", "chat")
    model_choice = data.get("model")
    user_id = data.get("userId")

    # Log incoming message and its type
    logger.info(f"Received user message for [{chat_id}]: {content}")
    logger.info(f"Message mode for [{chat_id}]: {message_mode}")
    if model_choice:
        logger.info(f"Model choice for [{chat_id}]: {model_choice}")

    # ── On every new user message, clear out any previous user_uploads ──
    uploads_path = get_user_uploads_dir(chat_id)
    uploads_path.mkdir(parents=True, exist_ok=True)
    for p in uploads_path.iterdir():
        try:
            p.unlink()
        except Exception:
            # ignore dirs or locked files
            pass
    logger.info("Cleared user_uploads folder")
    logger.info(f"Attachments metadata: {attachments}")

    # # ── Persist each attachment into PUBLIC_PDF_PATH ──
    # # initialize R2/S3 client
    # client = boto3.client(
    #     "s3",
    #     endpoint_url=str(settings.R2_ENDPOINT),
    #     region_name="auto",
    #     aws_access_key_id=settings.R2_ACCESS_KEY_ID,
    #     aws_secret_access_key=settings.R2_SECRET_ACCESS_KEY,
    # )
    # # for each non-image: OCR→.md
    # # for each user‐provided attachment, once it has a real key we can download & OCR it
    # for att in attachments:
    #     key  = att.get("key")
    #     name = att.get("name")

    #     # if the front-end sent us a placeholder (key === ""), we can't download yet
    #     if not key:
    #         logger.info(f"Attachment {name!r} not yet uploaded to R2; skipping for now")
    #         continue
    #     ext  = name.rsplit(".",1)[-1].lower()

    #     # only OCR documents
    #     if ext not in ("pdf","docx","pptx"):
    #         continue

    #     # 1️⃣ Download the original file into user_uploads/
    #     UP = Path(settings.USER_UPLOADS_DIR)
    #     UP.mkdir(exist_ok=True, parents=True)
    #     dest_pdf = UP / name
    #     try:
    #         obj = client.get_object(Bucket=settings.R2_BUCKET_NAME, Key=key)
    #         bytes_data = obj["Body"].read()
    #         dest_pdf.write_bytes(bytes_data)
    #         logger.info(f"Saved user upload → {dest_pdf}")
    #     except Exception as e:
    #         logger.error(f"Failed to download {name} for local copy: {e}")

    #     # 2️⃣ Generate a presigned GET so Mistral can fetch it
    #     try:
    #         get_url = client.generate_presigned_url(
    #             ClientMethod="get_object",
    #             Params={"Bucket": settings.R2_BUCKET_NAME, "Key": key},
    #             ExpiresIn=300,
    #         )
    #     except Exception as e:
    #         logger.error(f"Failed to gen presign GET for {key}: {e}")
    #         continue

    #     # 3️⃣ OCR → markdown
    #     try:
    #         md_path = await ocr_document_to_markdown(get_url)
    #         logger.info(f"OCR’d {name} → {md_path}")
    #     except Exception as e:
    #         logger.error(f"Failed OCR for {name}: {e}")

    # ── spin up one task per attachment (documents → OCR, images → download)
    client = boto3.client(
        "s3",
        endpoint_url=str(settings.R2_ENDPOINT),
        region_name="auto",
        aws_access_key_id=settings.R2_ACCESS_KEY_ID,
        aws_secret_access_key=settings.R2_SECRET_ACCESS_KEY,
    )

    tasks = []
    # keep a simple counter for images
    image_counter = 0
    for att in attachments:
        key = att.get("key") or ""
        name = att.get("name") or "unknown"
        # Fallback: if the frontend sent only a URL, derive the key
        if not key and att.get("url"):
            key = urlparse(att["url"]).path.lstrip("/")
        # skip placeholders not yet uploaded
        if not key:
            logger.info(f"Skipping attachment {name!r}: no key")
            continue

        ext = name.rsplit(".", 1)[-1].lower()
        # presign GET once for all
        get_url = client.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": settings.R2_BUCKET_NAME, "Key": key},
            ExpiresIn=300,
        )

        if ext in ("pdf", "docx", "pptx", "html", "csv", "xml"):
            tasks.append(ocr_document_to_markdown(get_url, chat_id))
        else:
            # keep the raw image file so we can pass it to TaskPlanner later
            image_counter += 1
            image_name = f"upload_image_{image_counter}.{ext}"
            tasks.append(save_user_image(client, key, image_name, chat_id))

    # run them all in parallel
    results = await asyncio.gather(*tasks, return_exceptions=True)
    for r in results:
        if isinstance(r, Exception):
            logger.error(f"Attachment processing error: {r}")
        else:
            logger.info(f"Attachment processed → {r}")

    # Optionally record these local filenames/URLs in your DB:
    # local_urls = [f"/pdfs/{dest.name}" for dest in PUBLIC_PDF_PATH.iterdir() if ... ]
    # chat_histories_collection.update_one(
    #   {"conversation_id": chat_id},
    #   {"$push": {"messages": {"role":"user","content": content,"attachments": local_urls,"timestamp": datetime.utcnow()}}}
    # )

    # Optionally store the user message + attachments in your DB here:
    # chat_histories_collection.update_one(
    #     {"conversation_id": chat_id},
    #     {"$push": {"messages": {
    #         "role":"user",
    #         "content": content,
    #         "attachments": attachments,
    #         "timestamp": datetime.utcnow().isoformat()
    #     }}})
    await sio.emit("chat_response", {"chatId": chat_id, "data": "[START]"}, room=sid)
    # now tell process_message which chatId to use
    # collect any OCR’d markdown and inject into task_details
    docs_text = collect_uploaded_docs(chat_id)
    images_list = collect_uploaded_images(chat_id)

    token = current_chat_id.set(chat_id)
    user_token = current_user_id.set(user_id or "")
    if message_mode == "command":
        response_generator = command.process_message(
            content,
            chat_id,
            user_id,
            docs_text,
            images_list,
            message_id=message_id,
            attachments=attachments,
            message_mode=message_mode,
        )
    else:
        response_generator = chat.process_message(
            content,
            chat_id,
            user_id,
            model_choice,
            message_id=message_id,
            attachments=attachments,
            message_mode=message_mode,
        )

    async for chunk in response_generator:
        await sio.emit("chat_response", {"chatId": chat_id, "data": chunk}, room=sid)

    await sio.emit("chat_response", {"chatId": chat_id, "data": "[END]"}, room=sid)
    current_chat_id.reset(token)
    current_user_id.reset(user_token)


@sio.on("store_ai_message")
async def store_ai(sid, data):
    """
    data = {
      "chatId": string,
      "timestamp": iso-string,
      "runtimeLogs": [ { subtaskId, description, status, tsStart, tsEnd? }, … ],
      "content": string
    }
    """
    chat_id = data.get("chatId")
    if not chat_id:
        return

    def _extract_agent(description: str) -> str:
        """Return the first ``<agent>`` tag found in ``description``."""
        for line in description.splitlines():
            line = line.strip()
            if line.startswith("<") and ":" in line:
                agent = line[1 : line.find(">")]
                return agent
        return ""

    raw_logs = data.get("runtimeLogs", [])
    runtime_logs = []
    for log in raw_logs:
        desc = log.get("description", "")
        runtime_logs.append(
            {
                "subtask_id": log.get("subtaskId"),
                "description": desc,
                "status": log.get("status"),
                "ts_start": log.get("tsStart"),
                "ts_end": log.get("tsEnd"),
                "agent": _extract_agent(desc),
            }
        )

    ts_raw = data.get("timestamp")
    try:
        ts_obj = datetime.fromisoformat(ts_raw)
    except Exception:
        ts_obj = datetime.utcnow()

    # Attach runtime logs to the previous user message
    doc = chat_histories_collection.find_one({"conversation_id": chat_id})
    if doc:
        msgs = doc.get("messages", [])
        idx = None
        for i in range(len(msgs) - 1, -1, -1):
            if msgs[i].get("role") == "user":
                idx = i
                break
        if idx is not None:
            chat_histories_collection.update_one(
                {"conversation_id": chat_id},
                {"$set": {f"messages.{idx}.runtime_logs": runtime_logs}},
            )

    ai_doc = {
        "role": "ai",
        "message_id": data.get("messageId"),
        "timestamp": ts_obj,
        "content": data.get("content", ""),
        "feedback": None,
    }

    chat_histories_collection.update_one(
        {"conversation_id": chat_id},
        {
            "$push": {"messages": ai_doc},
            "$set": {"updated_at": ts_obj, "last_message_at": ts_obj},
        },
    )

    # Update user usage stats from agent_analysis.log then clear it
    if doc:
        user_id = str(doc.get("user_id"))
        token = current_user_id.set(user_id)
        log_dir = get_chat_log_dir(user_id, chat_id)
        analysis_file = log_dir / "agent_analysis.log"
        analysis_file.touch(exist_ok=True)
        try:
            log_text = analysis_file.read_text()
            totals = tally_tokens(log_text)
            costs = calculate_costs(totals)
            total_cost = costs.get("__overall_total__", {}).get("total_cost", 0.0)
            users_collection.update_one(
                {"_id": ObjectId(user_id)},
                {
                    "$inc": {
                        "usage.messages": 1,
                        "usage.total_cost": total_cost,
                    },
                    "$push": {
                        "usage.history": {
                            "messageId": data.get("messageId"),
                            "type": "ai",
                            "cost": total_cost,
                        }
                    },
                },
                upsert=True,
            )
            analysis_file.write_text("")
        except Exception as exc:  # noqa: BLE001
            logger.error(f"Failed to update usage: {exc}")
        current_user_id.reset(token)

    logger.info(f"Stored AI message + logs for {chat_id}")


@sio.on("feedback")
async def store_feedback(sid, data):
    """Record user feedback for the last AI response."""
    chat_id = data.get("chatId")
    message_id = data.get("messageId")
    value = data.get("value")
    if not chat_id or value not in ("up", "down"):
        return
    try:
        doc = chat_histories_collection.find_one({"conversation_id": chat_id})
        if not doc:
            return
        messages = doc.get("messages", [])
        idx = None
        if message_id:
            for i, msg in enumerate(messages):
                if msg.get("message_id") == message_id:
                    idx = i
                    break
        if idx is None:
            for i in range(len(messages) - 1, -1, -1):
                if messages[i].get("role") == "ai":
                    idx = i
                    break
        if idx is not None:
            chat_histories_collection.update_one(
                {"conversation_id": chat_id},
                {"$set": {f"messages.{idx}.feedback": value}},
            )
            logger.info(f"Received feedback for {chat_id}: {value}")
    except Exception as e:
        logger.error(f"Failed to record feedback: {e}")


# ──────────────────────────────────────────────────
#                    SECURE CHAT HANDLERS
# ──────────────────────────────────────────────────

@sio.on("secure_chat_created")
async def new_secure_chat(sid, data):
    """Seed a fresh secure chat history document for this chatId."""
    chat_id = data.get("chatId")
    user_id = data.get("userId")
    if not chat_id:
        return

    now = datetime.utcnow()
    doc = {
        "conversation_id": chat_id,
        "user_id": ObjectId(user_id) if user_id else ADMIN_USER_ID,
        "title": "Untitled Secure Chat",
        "preview": "",
        "created_at": now,
        "updated_at": now,
        "last_message_at": None,
        "is_favorite": False,
        "is_task": False,
        "is_secure": True,  # Mark as secure chat
        "messages": [],
    }
    token = current_user_id.set(user_id or "")
    chat_histories_collection.insert_one(doc)

    if user_id:
        get_chat_log_dir(user_id, chat_id)

    logger.info(f"Created secure chat document for {chat_id}")


@sio.on("secure_chat_message")
async def handle_secure_message(sid, data):
    """
    Handle secure chat message with encryption.
    data = {
      "chatId": ...,
      "messageId": ...,
      "content": ...,
      "messageMode": ...
    }
    """
    from app.api.routes.secure_chat import process_secure_message

    content = data["content"]
    attachments = data.get("attachments", [])
    chat_id = data["chatId"]
    message_id = data.get("messageId")
    message_mode = data.get("messageMode", "chat")
    model_choice = data.get("model")
    user_id = data.get("userId")

    logger.info(f"Received secure user message for [{chat_id}]: [ENCRYPTED]")
    logger.info(f"Message mode for [{chat_id}]: {message_mode}")
    if model_choice:
        logger.info(f"Model choice for [{chat_id}]: {model_choice}")

    try:
        async for chunk in process_secure_message(
            content,
            chat_id,
            user_id,
            model_choice,
            message_id=message_id,
            attachments=attachments,
            message_mode=message_mode,
        ):
            await sio.emit("secure_chat_chunk", {"chatId": chat_id, "chunk": chunk}, room=sid)
    except Exception as e:
        logger.error(f"Error in secure chat processing: {e}")
        await sio.emit("secure_chat_error", {"chatId": chat_id, "error": str(e)}, room=sid)


@sio.on("store_secure_ai_message")
async def store_secure_ai(sid, data):
    """
    Store AI message with encryption.
    data = {
      "chatId": string,
      "timestamp": iso-string,
      "runtimeLogs": [...],
      "content": string
    }
    """
    from app.utils.encryption import encrypt, EncryptionError

    chat_id = data.get("chatId")
    if not chat_id:
        return

    def _extract_agent(description: str) -> str:
        """Return the first ``<agent>`` tag found in ``description``."""
        for line in description.splitlines():
            line = line.strip()
            if line.startswith("<") and ":" in line:
                agent = line[1 : line.find(">")]
                return agent
        return ""

    try:
        # Encrypt the AI response content
        ai_content = data.get("content", "")
        encrypted_content = encrypt(ai_content)

        raw_logs = data.get("runtimeLogs", [])
        runtime_logs = []
        for log in raw_logs:
            desc = log.get("description", "")
            runtime_logs.append(
                {
                    "subtask_id": log.get("subtaskId"),
                    "description": desc,
                    "status": log.get("status"),
                    "ts_start": log.get("tsStart"),
                    "ts_end": log.get("tsEnd"),
                    "agent": _extract_agent(desc),
                }
            )

        ts_raw = data.get("timestamp")
        try:
            ts_obj = datetime.fromisoformat(ts_raw)
        except Exception:
            ts_obj = datetime.utcnow()

        # Attach runtime logs to the previous user message
        doc = chat_histories_collection.find_one({"conversation_id": chat_id, "is_secure": True})
        if doc:
            msgs = doc.get("messages", [])
            idx = None
            for i in range(len(msgs) - 1, -1, -1):
                if msgs[i].get("role") == "user":
                    idx = i
                    break
            if idx is not None:
                chat_histories_collection.update_one(
                    {"conversation_id": chat_id, "is_secure": True},
                    {"$set": {f"messages.{idx}.runtime_logs": runtime_logs}},
                )

        # Store encrypted AI message
        ai_message = {
            "role": "ai",
            "message_id": data.get("messageId"),
            "content": encrypted_content,  # Store encrypted
            "timestamp": ts_obj,
            "feedback": None,
        }

        chat_histories_collection.update_one(
            {"conversation_id": chat_id, "is_secure": True},
            {
                "$push": {"messages": ai_message},
                "$set": {"updated_at": ts_obj, "last_message_at": ts_obj},
            },
        )

        logger.info(f"Stored encrypted AI message for secure chat {chat_id}")

    except EncryptionError as e:
        logger.error(f"Failed to encrypt AI message for {chat_id}: {e}")
    except Exception as e:
        logger.error(f"Error storing secure AI message: {e}")
