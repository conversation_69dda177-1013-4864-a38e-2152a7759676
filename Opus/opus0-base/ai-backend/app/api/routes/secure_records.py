# app/api/routes/secure_records.py
"""
Secure Records API for retrieving encrypted chat documents.

This module mirrors records.py but decrypts message content before
returning to the frontend.
"""

import logging
from fastapi import APIRouter, HTTPException

from bson import ObjectId
from app.db import chat_histories_collection
from app.db.transform import chat_doc_to_model
from app.api.routes.chat import _serialize_attachments
from app.utils.encryption import decrypt, EncryptionError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/secure_chats", tags=["secure_chats"])


@router.get("/{chat_id}")
def fetch_secure_chat(chat_id: str, user_id: str | None = None) -> dict:
    """Return the full secure chat document with decrypted messages."""
    query = {"conversation_id": chat_id, "is_secure": True}
    if user_id:
        query["user_id"] = ObjectId(user_id)
    
    doc = chat_histories_collection.find_one(query)
    if not doc:
        raise HTTPException(status_code=404, detail="Secure chat not found")

    # Decrypt all message content before transforming
    decrypted_doc = doc.copy()
    decrypted_messages = []
    
    for msg in doc.get("messages", []):
        try:
            decrypted_msg = msg.copy()
            decrypted_msg["content"] = decrypt(msg["content"])
            decrypted_messages.append(decrypted_msg)
        except EncryptionError as e:
            logger.error(f"Failed to decrypt message in chat {chat_id}: {e}")
            # Return error message instead of failing entirely
            decrypted_msg = msg.copy()
            decrypted_msg["content"] = "[DECRYPTION ERROR]"
            decrypted_messages.append(decrypted_msg)
    
    decrypted_doc["messages"] = decrypted_messages
    
    chat = chat_doc_to_model(decrypted_doc)
    for msg in chat.get("messages", []):
        msg["attachments"] = _serialize_attachments(msg.get("attachments", []))
    
    return chat


@router.get("")
def list_secure_chats(user_id: str | None = None) -> list[dict]:
    """Return basic summaries for all secure chats."""
    query = {"is_secure": True}
    if user_id:
        query["user_id"] = ObjectId(user_id)
    
    docs = chat_histories_collection.find(query).sort("created_at", -1)
    summaries = []
    
    for doc in docs:
        model = chat_doc_to_model(doc)
        model.pop("messages", None)  # Don't include messages in summary
        summaries.append(model)
    
    return summaries


@router.delete("/{chat_id}")
def delete_secure_chat(chat_id: str) -> dict:
    """Remove the secure chat document."""
    result = chat_histories_collection.delete_one({
        "conversation_id": chat_id, 
        "is_secure": True
    })
    if not result.deleted_count:
        raise HTTPException(status_code=404, detail="Secure chat not found")
    
    logger.info(f"Deleted secure chat history for {chat_id}")
    return {"deleted": True}
