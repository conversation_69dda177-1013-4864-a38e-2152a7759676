# app/api/routes/secure_chat.py
"""
Secure Chat API for encrypted conversations.

This module mirrors the regular chat.py but encrypts all message content
before storing in MongoDB and decrypts when streaming back to frontend.
"""

import logging
from datetime import datetime
from typing import AsyncGenerator

from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import Runnable

from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
from langchain_google_genai import ChatGoogle<PERSON>ener<PERSON><PERSON><PERSON>

from bson import ObjectId
from app.core.config import settings
from app.db import chat_histories_collection
from app.utils.encryption import encrypt, decrypt, EncryptionError

logger = logging.getLogger(__name__)

# Model mapping (same as regular chat.py)
_MODEL_MAP = {
    "Gemini 2.5 Flash": "gemini-2.5-flash",
    "Gemini 1.5 Pro": "gemini-1.5-pro",
    "GPT-4o": "gpt-4o",
    "GPT-4o Mini": "gpt-4o-mini",
}

prompt = ChatPromptTemplate.from_template(
    "You are a helpful AI assistant. Here is the conversation history:\n\n{chat_history}\n\nUser: {input}\n\nAssistant:"
)


def build_chain(model_choice: str | None) -> Runnable:
    """Return a LangChain chain using the chosen model."""
    name = _MODEL_MAP.get(model_choice or "Gemini 2.5 Flash", "gemini-2.5-flash")
    if name.startswith("gemini"):
        llm = ChatGoogleGenerativeAI(api_key=settings.GOOGLE_API_KEY, model=name)
    else:
        llm = ChatOpenAI(api_key=settings.OPENAI_API_KEY, model=name)
    return prompt | llm | StrOutputParser()


def _serialize_attachments(atts: list[dict] | None) -> list[dict]:
    """Return public attachment metadata with download URLs."""
    if not atts:
        return []
    return [
        {
            "name": a.get("name", ""),
            "size": a.get("size", ""),
            "url": f"/docs/{a.get('key')}" if a.get("key") else "",
        }
        for a in atts
        if a.get("key")
    ]


# ──────────────────────────────────────────────────
#                         PUBLIC API
# ──────────────────────────────────────────────────
async def process_secure_message(
    message: str,
    chat_id: str,
    user_id: str,
    model_choice: str | None = None,
    *,
    message_id: str | None = None,
    attachments: list[dict] | None = None,
    message_mode: str = "chat",
) -> AsyncGenerator[str, None]:
    """Generate a reply using the chosen model and stream the conversation with encryption."""
    
    # Retrieve or create secure chat history document
    chat_history_doc = chat_histories_collection.find_one({"conversation_id": chat_id})
    if not chat_history_doc:
        now = datetime.utcnow()
        auto_title = message if len(message) <= 50 else f"{message[:47]}..."
        chat_history_doc = {
            "conversation_id": chat_id,
            "user_id": ObjectId(user_id),
            "title": auto_title,
            "preview": "",
            "created_at": now,
            "updated_at": now,
            "last_message_at": None,
            "is_favorite": False,
            "is_task": False,
            "is_secure": True,  # Mark as secure chat
            "messages": [],
        }
        chat_histories_collection.insert_one(chat_history_doc)
        logger.info(f"Created secure chat_history for {chat_id}")
    
    # Get chat history and decrypt messages for LLM context
    chat_history = chat_history_doc.get("messages", [])
    decrypted_history = []
    
    for msg in chat_history:
        try:
            decrypted_msg = msg.copy()
            decrypted_msg["content"] = decrypt(msg["content"])
            decrypted_history.append(decrypted_msg)
        except EncryptionError as e:
            logger.error(f"Failed to decrypt message in chat {chat_id}: {e}")
            # Skip corrupted messages rather than failing entirely
            continue
    
    if not decrypted_history and chat_history_doc.get("title") == "Untitled Chat":
        auto_title = message if len(message) <= 50 else f"{message[:47]}..."
        chat_histories_collection.update_one(
            {"conversation_id": chat_id}, {"$set": {"title": auto_title}}
        )

    # Encrypt and store user message
    try:
        encrypted_content = encrypt(message)
    except EncryptionError as e:
        logger.error(f"Failed to encrypt user message: {e}")
        raise

    now = datetime.utcnow()
    clean_atts = [
        {"key": a.get("key"), "name": a.get("name"), "size": a.get("size")}
        for a in (attachments or [])
    ]

    new_message = {
        "role": "user",
        "message_id": message_id,
        "content": encrypted_content,  # Store encrypted
        "attachments": clean_atts,
        "message_mode": message_mode,
        "timestamp": now,
    }
    
    chat_histories_collection.update_one(
        {"conversation_id": chat_id},
        {
            "$push": {"messages": new_message},
            "$set": {"updated_at": now, "last_message_at": now},
        },
    )
    logger.info(f"Appended encrypted USER message → {chat_id}")
    
    # Add decrypted message to history for LLM processing
    decrypted_new_message = new_message.copy()
    decrypted_new_message["content"] = message  # Use original plaintext
    decrypted_history.append(decrypted_new_message)

    # Format history for the model (using decrypted content)
    formatted = ""
    for msg in decrypted_history:
        role = "User" if msg["role"] == "user" else "Assistant"
        formatted += f"{role}: {msg['content']}\n"

    inputs = {"chat_history": formatted, "input": message}

    chain = build_chain(model_choice)

    # Stream model response while collecting the final text
    chunks: list[str] = []
    async for chunk in chain.astream(inputs):
        chunks.append(chunk)
        yield chunk

    # The frontend stores the AI message via ``store_secure_ai_message``.
    # ``result`` holds the complete assistant reply if needed by callers.
    result = "".join(chunks)
