#!/usr/bin/env python3
"""
Real SecureChat Application Flow Test

This test actually uses the application's socket handlers and API endpoints
to create secure chats, send messages to different models, and verify that:
1. MongoDB stores only encrypted content
2. API endpoints return properly decrypted content
3. The complete user flow works end-to-end
"""

import os
import sys
import asyncio
import time
import socketio
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

# Set test environment
os.environ["CHAT_ENCRYPTION_KEY"] = "ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg="

class RealSecureChatTest:
    """Test SecureChat using actual application flow."""
    
    def __init__(self):
        self.test_chat_id = f"real_test_{int(time.time())}"
        self.test_user_id = "507f1f77bcf86cd799439011"
        self.test_messages = [
            {
                "content": "Hello! Can you explain quantum computing in simple terms?",
                "model": "Gemini 2.5 Flash",
                "expected_ai_response_contains": ["quantum", "computing"]
            },
            {
                "content": "What are the main differences between Python and JavaScript?",
                "model": "GPT-4o Mini", 
                "expected_ai_response_contains": ["Python", "JavaScript"]
            }
        ]
        self.ai_responses = []
        self.db_collection = None
        
    def setup_database(self):
        """Initialize database connection for verification."""
        try:
            from app.db import chat_histories_collection
            self.db_collection = chat_histories_collection
            print("✅ Database connection established")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
    
    def cleanup_test_data(self):
        """Clean up test data from database."""
        if self.db_collection is not None:
            result = self.db_collection.delete_many({
                "conversation_id": {"$regex": "^real_test_"}
            })
            if result.deleted_count > 0:
                print(f"🧹 Cleaned up {result.deleted_count} test chat documents")
    
    async def test_secure_chat_creation(self):
        """Test creating a secure chat using the actual socket handler."""
        print("\n📝 Testing secure chat creation via socket handler...")
        
        try:
            # Import the actual socket handler
            from app.main import new_secure_chat
            
            # Simulate socket data
            socket_data = {
                "chatId": self.test_chat_id,
                "userId": self.test_user_id
            }
            
            # Call the actual socket handler
            await new_secure_chat("test_sid", socket_data)
            
            # Verify chat was created in database
            chat_doc = self.db_collection.find_one({"conversation_id": self.test_chat_id})
            
            if not chat_doc:
                print("❌ Secure chat document not created")
                return False
            
            if not chat_doc.get("is_secure"):
                print("❌ Chat not marked as secure")
                return False
            
            print(f"✅ Secure chat created successfully")
            print(f"   - Chat ID: {self.test_chat_id}")
            print(f"   - is_secure: {chat_doc.get('is_secure')}")
            print(f"   - Title: {chat_doc.get('title')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Secure chat creation failed: {e}")
            return False
    
    async def test_secure_message_flow(self, message_data):
        """Test sending a message and getting AI response using actual handlers."""
        print(f"\n💬 Testing secure message flow with {message_data['model']}...")
        
        try:
            # Import the actual socket handler
            from app.main import handle_secure_message, store_secure_ai
            
            # Prepare message data
            socket_data = {
                "chatId": self.test_chat_id,
                "content": message_data["content"],
                "messageId": f"msg_{int(time.time())}",
                "model": message_data["model"],
                "userId": self.test_user_id,
                "messageMode": "chat"
            }
            
            print(f"   Sending message: {message_data['content'][:50]}...")
            
            # Collect AI response chunks
            ai_chunks = []
            
            # Mock socket emit to capture AI response
            class MockSocket:
                def __init__(self):
                    self.emitted_events = []
                
                async def emit(self, event, data, room=None):
                    self.emitted_events.append((event, data))
                    if event == "secure_chat_chunk":
                        ai_chunks.append(data.get("chunk", ""))
            
            mock_socket = MockSocket()
            
            # Temporarily replace the socket for testing
            import app.main
            original_sio = app.main.sio
            app.main.sio = mock_socket
            
            try:
                # Call the actual secure message handler
                await handle_secure_message("test_sid", socket_data)
                
                # Restore original socket
                app.main.sio = original_sio
                
                # Combine AI response
                ai_response = "".join(ai_chunks)
                self.ai_responses.append(ai_response)
                
                print(f"   AI response received: {len(ai_response)} characters")
                
                # Verify AI response contains expected content
                for expected in message_data["expected_ai_response_contains"]:
                    if expected.lower() not in ai_response.lower():
                        print(f"   ⚠️ AI response may not contain expected content: {expected}")
                
                # Store the AI response using the actual handler
                ai_store_data = {
                    "chatId": self.test_chat_id,
                    "content": ai_response,
                    "timestamp": "2024-01-01T00:00:00Z",
                    "runtimeLogs": []
                }
                
                await store_secure_ai("test_sid", ai_store_data)
                
                print(f"   ✅ Message flow completed successfully")
                return True
                
            finally:
                # Ensure socket is restored
                app.main.sio = original_sio
            
        except Exception as e:
            print(f"❌ Secure message flow failed: {e}")
            return False
    
    def verify_mongodb_encryption(self):
        """Verify that MongoDB contains only encrypted content."""
        print("\n🔍 Verifying MongoDB encryption...")
        
        try:
            # Get the chat document from MongoDB
            chat_doc = self.db_collection.find_one({"conversation_id": self.test_chat_id})
            
            if not chat_doc:
                print("❌ Chat document not found")
                return False
            
            messages = chat_doc.get("messages", [])
            if len(messages) < 2:  # Should have at least 2 user messages
                print(f"❌ Expected at least 2 messages, found {len(messages)}")
                return False
            
            # Check each message for encryption
            user_messages = [msg for msg in messages if msg.get("role") == "user"]
            ai_messages = [msg for msg in messages if msg.get("role") in ["ai", "assistant"]]
            
            print(f"   Found {len(user_messages)} user messages, {len(ai_messages)} AI messages")
            
            # Verify user messages are encrypted
            for i, msg in enumerate(user_messages):
                content = msg.get("content", "")
                original = self.test_messages[i]["content"]
                
                # Check it's encrypted (Fernet format)
                if not content.startswith("gAAAAA"):
                    print(f"❌ User message {i+1} not encrypted: {content}")
                    return False
                
                # Check it's not plaintext
                if content == original:
                    print(f"❌ User message {i+1} is plaintext in MongoDB!")
                    return False
                
                print(f"   ✅ User message {i+1} properly encrypted: {content[:30]}...")
            
            # Verify AI messages are encrypted
            for i, msg in enumerate(ai_messages):
                content = msg.get("content", "")
                
                # Check it's encrypted
                if not content.startswith("gAAAAA"):
                    print(f"❌ AI message {i+1} not encrypted: {content}")
                    return False
                
                # Check it's not the plaintext AI response
                if i < len(self.ai_responses) and content == self.ai_responses[i]:
                    print(f"❌ AI message {i+1} is plaintext in MongoDB!")
                    return False
                
                print(f"   ✅ AI message {i+1} properly encrypted: {content[:30]}...")
            
            print("✅ All messages properly encrypted in MongoDB")
            return True
            
        except Exception as e:
            print(f"❌ MongoDB encryption verification failed: {e}")
            return False
    
    def test_api_decryption(self):
        """Test that API endpoints return properly decrypted content."""
        print("\n🔓 Testing API decryption...")
        
        try:
            # Use the actual API endpoint
            from app.api.routes.secure_records import fetch_secure_chat
            
            # Fetch the chat via API
            chat_data = fetch_secure_chat(self.test_chat_id, self.test_user_id)
            
            if not chat_data:
                print("❌ Failed to fetch secure chat via API")
                return False
            
            # Verify chat metadata
            if chat_data.get("id") != self.test_chat_id:
                print(f"❌ Wrong chat ID returned: {chat_data.get('id')}")
                return False
            
            # Verify messages are decrypted
            messages = chat_data.get("messages", [])
            user_messages = [msg for msg in messages if msg.get("isUser")]
            
            if len(user_messages) < 2:
                print(f"❌ Expected 2 user messages, got {len(user_messages)}")
                return False
            
            # Check each user message is properly decrypted
            for i, msg in enumerate(user_messages):
                decrypted_content = msg.get("content", "")
                expected_content = self.test_messages[i]["content"]
                
                if decrypted_content != expected_content:
                    print(f"❌ Message {i+1} decryption failed:")
                    print(f"   Expected: {expected_content}")
                    print(f"   Got: {decrypted_content}")
                    return False
                
                print(f"   ✅ Message {i+1} correctly decrypted via API")
            
            print("✅ All messages properly decrypted via API")
            return True
            
        except Exception as e:
            print(f"❌ API decryption test failed: {e}")
            return False
    
    async def run_complete_test(self):
        """Run the complete real application test."""
        print("🚀 Real SecureChat Application Flow Test")
        print("=" * 60)
        print("Testing actual application flow:")
        print("• Create secure chat via socket handler")
        print("• Send messages to different AI models")
        print("• Verify MongoDB stores encrypted content")
        print("• Verify API returns decrypted content")
        print("=" * 60)
        
        # Setup
        if not self.setup_database():
            return False
        
        # Clean up any existing test data
        self.cleanup_test_data()
        
        success = True
        
        try:
            # Test 1: Create secure chat
            if not await self.test_secure_chat_creation():
                success = False
            
            # Test 2: Send messages to different models
            for i, message_data in enumerate(self.test_messages):
                print(f"\n--- Testing Message {i+1} ---")
                if not await self.test_secure_message_flow(message_data):
                    success = False
                
                # Small delay between messages
                await asyncio.sleep(1)
            
            # Test 3: Verify MongoDB encryption
            if not self.verify_mongodb_encryption():
                success = False
            
            # Test 4: Verify API decryption
            if not self.test_api_decryption():
                success = False
            
        finally:
            # Cleanup
            self.cleanup_test_data()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 REAL APPLICATION TEST PASSED!")
            print("\nVerified:")
            print("✅ Secure chat creation works via socket handlers")
            print("✅ Messages sent to different AI models work")
            print("✅ MongoDB stores only encrypted content")
            print("✅ API endpoints return properly decrypted content")
            print("✅ Complete user flow works end-to-end")
            print("\n🛡️ Your SecureChat is ready for real users!")
        else:
            print("❌ REAL APPLICATION TEST FAILED!")
            print("Check the output above for specific failures.")
        
        return success

async def main():
    """Run the real application test."""
    test_runner = RealSecureChatTest()
    success = await test_runner.run_complete_test()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
