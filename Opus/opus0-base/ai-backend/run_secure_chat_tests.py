#!/usr/bin/env python3
"""
Comprehensive SecureChat Test Suite Runner

This script runs all SecureChat tests and provides detailed reporting.
It verifies that the implementation meets all security requirements.
"""

import os
import sys
import asyncio
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

def setup_test_environment():
    """Set up the test environment with proper encryption key."""
    # Use a test encryption key
    os.environ["CHAT_ENCRYPTION_KEY"] = "ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg="
    print("🔧 Test environment configured")

def check_dependencies():
    """Check that all required dependencies are available."""
    print("📋 Checking dependencies...")
    
    try:
        import cryptography
        print("✅ cryptography library available")
    except ImportError:
        print("❌ cryptography library not found")
        print("   Install with: poetry install or pip install cryptography")
        return False
    
    try:
        from app.utils.encryption import encrypt, decrypt
        print("✅ Encryption utilities available")
    except ImportError as e:
        print(f"❌ Encryption utilities not available: {e}")
        return False
    
    try:
        from app.db import chat_histories_collection
        print("✅ Database connection available")
    except ImportError as e:
        print(f"❌ Database connection not available: {e}")
        return False
    
    try:
        from app.api.routes.secure_chat import process_secure_message
        from app.api.routes.secure_records import fetch_secure_chat
        print("✅ SecureChat API routes available")
    except ImportError as e:
        print(f"❌ SecureChat API routes not available: {e}")
        return False
    
    return True

async def run_integration_tests():
    """Run the comprehensive integration tests."""
    print("\n" + "="*60)
    print("🚀 RUNNING INTEGRATION TESTS")
    print("="*60)
    
    try:
        from test_secure_chat_integration import SecureChatIntegrationTest
        test_runner = SecureChatIntegrationTest()
        return await test_runner.run_all_tests()
    except Exception as e:
        print(f"❌ Integration tests failed to run: {e}")
        return False

def run_mongodb_verification():
    """Run the MongoDB encryption verification tests."""
    print("\n" + "="*60)
    print("🔒 RUNNING MONGODB ENCRYPTION VERIFICATION")
    print("="*60)
    
    try:
        from test_mongodb_encryption_verification import MongoDBEncryptionVerifier
        verifier = MongoDBEncryptionVerifier()
        return verifier.run_verification()
    except Exception as e:
        print(f"❌ MongoDB verification failed to run: {e}")
        return False

def run_basic_encryption_tests():
    """Run basic encryption functionality tests."""
    print("\n" + "="*60)
    print("🔐 RUNNING BASIC ENCRYPTION TESTS")
    print("="*60)
    
    try:
        from app.utils.encryption import encrypt, decrypt, EncryptionError
        
        # Test 1: Basic roundtrip
        original = "Test message for encryption"
        encrypted = encrypt(original)
        decrypted = decrypt(encrypted)
        
        if decrypted != original:
            print("❌ Basic encryption roundtrip failed")
            return False
        
        if encrypted == original:
            print("❌ Message not actually encrypted")
            return False
        
        if not encrypted.startswith("gAAAAA"):
            print("❌ Invalid Fernet token format")
            return False
        
        print("✅ Basic encryption roundtrip works")
        
        # Test 2: Error handling
        try:
            decrypt("invalid_token")
            print("❌ Should have raised EncryptionError for invalid token")
            return False
        except EncryptionError:
            print("✅ Error handling works correctly")
        
        # Test 3: Empty string
        empty_encrypted = encrypt("")
        empty_decrypted = decrypt(empty_encrypted)
        if empty_decrypted != "":
            print("❌ Empty string encryption failed")
            return False
        print("✅ Empty string encryption works")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic encryption tests failed: {e}")
        return False

def generate_test_report(results):
    """Generate a comprehensive test report."""
    print("\n" + "="*80)
    print("📊 SECURECHAT TEST REPORT")
    print("="*80)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"\nTest Summary:")
    print(f"  Total Tests: {total_tests}")
    print(f"  Passed: {passed_tests}")
    print(f"  Failed: {failed_tests}")
    print(f"  Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\nDetailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} - {test_name}")
    
    if all(results.values()):
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"\nSecurity Verification Complete:")
        print(f"✅ MongoDB never stores plaintext for secure chats")
        print(f"✅ Encryption/decryption works correctly in all scenarios")
        print(f"✅ End-to-end user flows work as expected")
        print(f"✅ Regular and secure chats are properly separated")
        print(f"✅ Error handling and edge cases work correctly")
        print(f"\n🛡️ Your SecureChat implementation is ready for production!")
        
        print(f"\nNext Steps:")
        print(f"1. Generate a production encryption key:")
        print(f"   python -c \"from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())\"")
        print(f"2. Add it to your .env file as CHAT_ENCRYPTION_KEY")
        print(f"3. Update your frontend to use secure chat endpoints")
        print(f"4. Test with real users in a staging environment")
        
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        print(f"\n🚨 Security Issues Detected:")
        for test_name, result in results.items():
            if not result:
                print(f"   - {test_name}")
        print(f"\nPlease review the test output above and fix the issues before deploying.")
    
    return all(results.values())

async def main():
    """Run all SecureChat tests."""
    print("🔒 SecureChat Comprehensive Test Suite")
    print("="*80)
    print("This test suite verifies that:")
    print("• MongoDB never stores plaintext for secure chats")
    print("• Encryption/decryption works correctly")
    print("• End-to-end user scenarios work as expected")
    print("• Security requirements are met")
    print("="*80)
    
    # Setup
    setup_test_environment()
    
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install required packages.")
        sys.exit(1)
    
    # Run all test suites
    results = {}
    
    # Basic encryption tests
    results["Basic Encryption Functionality"] = run_basic_encryption_tests()
    
    # MongoDB verification tests
    results["MongoDB Encryption Verification"] = run_mongodb_verification()
    
    # Integration tests
    results["End-to-End Integration Tests"] = await run_integration_tests()
    
    # Generate report
    success = generate_test_report(results)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
