#!/usr/bin/env python3
"""
Test script for SecureChat encryption functionality.
Verifies that encryption/decryption works correctly and that
MongoDB stores only encrypted content.
"""

import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

def test_encryption_roundtrip():
    """Test that encrypt/decrypt works correctly."""
    print("Testing encryption roundtrip...")
    
    # Set a test encryption key (valid Fernet key for testing)
    os.environ["CHAT_ENCRYPTION_KEY"] = "ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg="
    
    try:
        from app.utils.encryption import encrypt, decrypt, EncryptionError
        
        # Test normal message
        original = "Hello, this is a secure message!"
        encrypted = encrypt(original)
        decrypted = decrypt(encrypted)
        
        print(f"Original: {original}")
        print(f"Encrypted: {encrypted}")
        print(f"Decrypted: {decrypted}")
        
        assert original == decrypted, "Decrypted message doesn't match original"
        assert encrypted != original, "Encrypted message should be different from original"
        assert encrypted.startswith("gAAAAA"), "Fernet tokens should start with gAAAAA"
        
        print("✅ Encryption roundtrip test passed!")
        
        # Test empty message
        empty_encrypted = encrypt("")
        empty_decrypted = decrypt(empty_encrypted)
        assert empty_encrypted == "", "Empty string should remain empty"
        assert empty_decrypted == "", "Empty string should decrypt to empty"
        
        print("✅ Empty message test passed!")
        
        # Test error handling
        try:
            decrypt("invalid_token")
            assert False, "Should have raised EncryptionError"
        except EncryptionError:
            print("✅ Error handling test passed!")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure dependencies are installed: pip install cryptography")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

def test_missing_key():
    """Test behavior when encryption key is missing."""
    print("\nTesting missing key handling...")

    # Remove the key
    if "CHAT_ENCRYPTION_KEY" in os.environ:
        del os.environ["CHAT_ENCRYPTION_KEY"]

    # Need to reload the module to pick up the environment change
    import importlib
    import sys
    if 'app.utils.encryption' in sys.modules:
        importlib.reload(sys.modules['app.utils.encryption'])

    try:
        from app.utils.encryption import encrypt, EncryptionError

        try:
            encrypt("test message")
            print("❌ Should have raised EncryptionError")
            return False
        except EncryptionError as e:
            print(f"✅ Correctly raised error: {e}")
            return True
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Run all tests."""
    print("SecureChat Encryption Tests")
    print("=" * 40)
    
    success = True
    
    # Test encryption functionality
    if not test_encryption_roundtrip():
        success = False
    
    # Test missing key handling
    if not test_missing_key():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All tests passed!")
        print("\nNext steps:")
        print("1. Generate a real encryption key:")
        print("   python -c \"from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())\"")
        print("2. Add it to your .env file as CHAT_ENCRYPTION_KEY")
        print("3. Test the secure chat endpoints with your frontend")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
